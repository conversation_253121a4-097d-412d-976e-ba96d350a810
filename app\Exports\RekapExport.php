<?php

namespace App\Exports;

use App\Models\User;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;

class RekapExport implements FromQuery, WithColumnFormatting, WithMapping, WithHeadings, ShouldAutoSize, WithStyles
{
    use Exportable;

    public function styles(Worksheet $sheet)
    {
        $highestColumn = $sheet->getHighestColumn();
        $highestRow = $sheet->getHighestRow();

        //BORDER
        $sheet->getStyle("A1:$highestColumn" . $highestRow)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        // HEADER
        $sheet->getStyle("A1:" . $highestColumn . "1")->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

        // CONTENT
        $sheet->getStyle("B:J")->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
        $sheet->getColumnDimension("B")->setAutoSize(false)->setWidth(10);
        $sheet->getColumnDimension("C")->setAutoSize(false)->setWidth(10);
        $sheet->getColumnDimension("D")->setAutoSize(false)->setWidth(10);
        $sheet->getColumnDimension("E")->setAutoSize(false)->setWidth(10);
        $sheet->getColumnDimension("F")->setAutoSize(false)->setWidth(10);
        $sheet->getColumnDimension("G")->setAutoSize(false)->setWidth(10);
        $sheet->getColumnDimension("H")->setAutoSize(false)->setWidth(10);
        $sheet->getColumnDimension("I")->setAutoSize(false)->setWidth(10);
        $sheet->getColumnDimension("J")->setAutoSize(false)->setWidth(10);
        $sheet->getColumnDimension("K")->setAutoSize(false)->setWidth(10);

        // WRAP TEXT
        $sheet->getStyle("A1:$highestColumn" . $highestRow)->getAlignment()->setWrapText(true);

        // ALIGNMENT TEXT
        $sheet->getStyle("A1:$highestColumn" . $highestRow)->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

        //BOLD FIRST ROW
        return [
            // Style the first row as bold text.
            1    => ['font' => ['bold' => true]],
        ];
    }

    public function headings(): array
    {
        return [
            'Nama Siswa',
            'Hadir',
            'Izin Sakit',
            'Izin Tidak Masuk',
            'Izin Telat',
            'Izin Pulang Cepat',
            'Total Alpha',
            'Total Libur',
            'Total Telat',
            'Total Pulang Cepat',
            // 'Total Lembur',
            '% Kehadiran',
        ];
    }

    public function map($model): array
    {
        $tanggal_mulai = request()->input('mulai');
        $tanggal_akhir = request()->input('akhir');

        $izin_sakit = $model->MappingShift->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])->where('status_absen', 'Izin Sakit')->count();
        $cuti = $model->MappingShift->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])->where('status_absen', 'Cuti')->count();
        $izin_masuk = $model->MappingShift->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])->where('status_absen', 'Izin Tidak Masuk')->count();
        $izin_telat = $model->MappingShift->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])->where('status_absen', 'Izin Telat')->count();
        $izin_pulang_cepat = $model->MappingShift->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])->where('status_absen', 'Izin Pulang Cepat')->count();
        $masuk = $model->MappingShift->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])->where('status_absen', 'Masuk')->count();

        $total_hadir = $masuk + $izin_telat + $izin_pulang_cepat;

        $libur = $model->MappingShift->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])->where('status_absen', 'Libur')->count();
        $mulai = new \DateTime($tanggal_mulai);
        $akhir = new \DateTime($tanggal_akhir);
        $interval = $mulai->diff($akhir);
        $total_alfa = $interval->days + 1 - $masuk - $cuti - $izin_masuk - $libur;
        $total_telat = $model->MappingShift->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])->sum('telat');
        $jam   = floor($total_telat / (60 * 60));
        $menit = $total_telat - ($jam * (60 * 60));
        $menit2 = floor($menit / 60);
        $jumlah_telat = $model->MappingShift->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])->where('telat', '>', 0)->count();
        $total_pulang_cepat = $model->MappingShift->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])->sum('pulang_cepat');
        $jam_cepat   = floor($total_pulang_cepat / (60 * 60));
        $menit_cepat = $total_pulang_cepat - ($jam_cepat * (60 * 60));
        $menit_cepat2 = floor($menit_cepat / 60);
        $jumlah_pulang_cepat = $model->MappingShift->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])->where('pulang_cepat', '>', 0)->count();
        $total_lembur = $model->Lembur->where('status', 'Approved')->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])->sum('total_lembur');
        $jam_lembur   = floor($total_lembur / (60 * 60));
        $menit_lembur = $total_lembur - ($jam_lembur * (60 * 60));
        $menit_lembur2 = floor($menit_lembur / 60);
        $timestamp_mulai = strtotime($tanggal_mulai);
        $timestamp_akhir = strtotime($tanggal_akhir);
        $selisih_timestamp = $timestamp_akhir - $timestamp_mulai;
        $jumlah_hari = (floor($selisih_timestamp / (60 * 60 * 24))) + 1;
        $persentase_kehadiran = (($total_hadir + $libur) / $jumlah_hari) * 100;

        return [
            $model->name,
            $total_hadir,
            $izin_sakit,
            $izin_masuk,
            $izin_telat,
            $izin_pulang_cepat,
            $total_alfa,
            $libur,
            str_pad($jam, 2, '0', STR_PAD_LEFT) . ":" . str_pad($menit2, 2, '0', STR_PAD_LEFT),
            // $jam . " Jam " . $menit2 . " Menit\n" . $jumlah_telat . " x",
            str_pad($jam_cepat, 2, '0', STR_PAD_LEFT) . ":" . str_pad($menit_cepat2, 2, '0', STR_PAD_LEFT),
            // $jam_lembur . " Jam " . $menit_lembur2 . " Menit",
            number_format($persentase_kehadiran, 2),
        ];
    }

    public function columnFormats(): array
    {
        return [];
    }

    public function query()
    {
        return User::select('id', 'name')->where('is_admin', 'user')->orderBy('name', 'ASC');
    }
}
