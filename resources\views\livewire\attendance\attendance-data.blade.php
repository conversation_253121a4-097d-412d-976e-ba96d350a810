<div>
    <div class="row mb-2 mb-xl-3">
        <div class="col-auto d-none d-sm-block">
            <h3>Data Absen</h3>
        </div>

        <div class="col-auto ms-auto text-end mt-n1">
            <a href="{{ url('/export-data-absen') }}{{ request()->getQueryString() ? '?' . request()->getQueryString() : '' }}"
                class="btn btn-success">
                <i class="fas fa-file-export"></i> Export
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12 col-xl-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-lg-2 col-sm-12 col-md-4 col-xxl-2 mb-2 mb-lg-0">
                            <select wire:model="lokasi_id" class="form-control">
                                <option value="">Semua <PERSON></option>
                                @foreach($lokasi as $l)
                                <option value="{{ $l->id }}">{{ $l->nama_lokasi }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-lg-2 col-sm-12 col-md-4 col-xxl-2 mb-2 mb-lg-0">
                            <select wire:model="jabatan_id" class="form-control">
                                <option value="">Semua Kelas</option>
                                @foreach($jabatan as $j)
                                <option value="{{ $j->id }}">{{ $j->nama_jabatan }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-lg-2 col-sm-12 col-md-4 col-xxl-2 mb-2 mb-lg-0">
                            <select wire:model="user_id" class="form-control">
                                <option value="">Semua Siswa</option>
                                @foreach($users as $u)
                                <option value="{{ $u->id }}">{{ $u->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-lg-2 col-sm-12 col-md-4 col-xxl-2 mb-2 mb-lg-0">
                            <input type="date" class="form-control" wire:model="mulai" placeholder="Tanggal Mulai">
                        </div>
                        <div class="col-lg-2 col-sm-12 col-md-4 col-xxl-2 mb-2 mb-lg-0">
                            <input type="date" class="form-control" wire:model="akhir" placeholder="Tanggal Akhir">
                        </div>
                        <div class="col-lg-2 col-sm-12 col-md-4 col-xxl-2 mb-2 mb-lg-0">
                            <button type="button" wire:click="resetFilters" class="btn btn-light">
                                <i class="fas fa-sync"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table mb-0">
                        <thead>
                            <tr>
                                <th width="50">No.</th>
                                <th>Nama Siswa</th>
                                <th>Jadwal</th>
                                <th>Tanggal</th>
                                <th>Jam Masuk</th>
                                <th>Telat</th>
                                <th>Lokasi Masuk</th>
                                <th>Foto Masuk</th>
                                <th>Jam Pulang</th>
                                <th>Pulang Cepat</th>
                                <th>Lokasi Pulang</th>
                                <th>Foto Pulang</th>
                                <th>Kegiatan</th>
                                <th>Status Absen</th>
                                <th>#</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php $no = ($data_absen->currentPage() - 1) * $data_absen->perPage() + 1; @endphp
                            @forelse($data_absen as $da)
                            <tr>
                                <td>{{ $no++ }}</td>
                                <td>{{ $da->User->name ?? '-' }}</td>
                                <td>{{ $da->jam_masuk ?? '-' }} - {{ $da->jam_keluar ?? '-' }}</td>
                                <td>{{ $da->tanggal }}</td>
                                <td>
                                    @if($da->jam_absen)
                                    <span class="badge bg-success">{{ $da->jam_absen }}</span>
                                    @else
                                    <span class="badge bg-secondary">-</span>
                                    @endif
                                </td>
                                <td>
                                    @if($da->telat > 0)
                                    <span class="badge bg-warning">{{ gmdate('H:i:s', $da->telat) }}</span>
                                    @else
                                    <span class="badge bg-success">Tepat Waktu</span>
                                    @endif
                                </td>
                                <td>
                                    @if($da->lat_absen && $da->long_absen)
                                    <a href="https://maps.google.com/?q={{ $da->lat_absen }},{{ $da->long_absen }}"
                                        target="_blank" class="btn btn-sm btn-info">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </a>
                                    @else
                                    -
                                    @endif
                                </td>
                                <td>
                                    @if($da->foto_jam_absen)
                                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal"
                                        data-bs-target="#fotoMasuk{{ $da->id }}">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    @else
                                    -
                                    @endif
                                </td>
                                <td>
                                    @if($da->jam_pulang)
                                    <span class="badge bg-success">{{ $da->jam_pulang }}</span>
                                    @else
                                    <span class="badge bg-secondary">-</span>
                                    @endif
                                </td>
                                <td>
                                    @if($da->pulang_cepat > 0)
                                    <span class="badge bg-warning">{{ gmdate('H:i:s', $da->pulang_cepat) }}</span>
                                    @else
                                    <span class="badge bg-success">Sesuai Jadwal</span>
                                    @endif
                                </td>
                                <td>
                                    @if($da->lat_pulang && $da->long_pulang)
                                    <a href="https://maps.google.com/?q={{ $da->lat_pulang }},{{ $da->long_pulang }}"
                                        target="_blank" class="btn btn-sm btn-info">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </a>
                                    @else
                                    -
                                    @endif
                                </td>
                                <td>
                                    @if($da->foto_jam_pulang)
                                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal"
                                        data-bs-target="#fotoPulang{{ $da->id }}">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    @else
                                    -
                                    @endif
                                </td>
                                <td>{{ $da->kegiatan ?? '-' }}</td>
                                <td>
                                    @php
                                    $statusClass = match($da->status_absen) {
                                    'Masuk' => 'bg-success',
                                    'Libur' => 'bg-info',
                                    'Izin Sakit', 'Izin Tidak Masuk', 'Izin Telat', 'Izin Pulang Cepat' => 'bg-warning',
                                    'Tidak Masuk' => 'bg-danger',
                                    default => 'bg-secondary'
                                    };
                                    @endphp
                                    <span class="badge {{ $statusClass }}">{{ $da->status_absen }}</span>
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-light dropdown-toggle" type="button"
                                            data-bs-toggle="dropdown">
                                            Aksi
                                        </button>
                                        <ul class="dropdown-menu">
                                            @if($da->jam_absen)
                                            <li><a class="dropdown-item"
                                                    href="{{ url('/data-absen/'.$da->id.'/edit-masuk') }}">Edit
                                                    Masuk</a></li>
                                            @endif
                                            @if($da->jam_pulang)
                                            <li><a class="dropdown-item"
                                                    href="{{ url('/data-absen/'.$da->id.'/edit-pulang') }}">Edit
                                                    Pulang</a></li>
                                            @endif
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="15" class="text-center">Tidak ada data</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <div class="card-footer">
                    {{ $data_absen->links() }}
                </div>
            </div>
        </div>
    </div>
</div>