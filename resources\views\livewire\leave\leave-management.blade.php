<div>
    <div class="row mb-2 mb-xl-3">
        <div class="col-auto">
            <h3>Data Cuti</h3>
        </div>
        <div class="col-auto ms-auto text-end mt-n1">
            <a href="{{ url('/data-cuti/tambah') }}" class="btn btn-primary">
                <i class="fa fa-plus"></i><span class="d-none d-md-inline"> Tambah</span>
            </a>
            <button wire:click="$set('status_filter', 'Diterima')"
                class="btn btn-success {{ $status_filter === 'Diterima' ? 'active' : '' }}">
                <i class="fa fa-check"></i><span class="d-none d-md-inline"> Diterima</span>
            </button>
            <button wire:click="$set('status_filter', 'Pending')"
                class="btn btn-warning {{ $status_filter === 'Pending' ? 'active' : '' }}">
                <i class="fa fa-clock"></i><span class="d-none d-md-inline"> Pending</span>
            </button>
            <button wire:click="$set('status_filter', 'Ditolak')"
                class="btn btn-danger {{ $status_filter === 'Ditolak' ? 'active' : '' }}">
                <i class="fa fa-times"></i><span class="d-none d-md-inline"> Ditolak</span>
            </button>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-lg-2 col-sm-12 col-md-4 col-xxl-2 mb-2 mb-lg-0">
                            <select wire:model="user_filter" class="form-control">
                                <option value="">Semua Siswa</option>
                                @foreach($users as $user)
                                <option value="{{ $user->id }}">{{ $user->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-lg-2 col-sm-12 col-md-4 col-xxl-2 mb-2 mb-lg-0">
                            <input type="date" class="form-control" wire:model="mulai" placeholder="Tanggal Mulai">
                        </div>
                        <div class="col-lg-2 col-sm-12 col-md-4 col-xxl-2 mb-2 mb-lg-0">
                            <input type="date" class="form-control" wire:model="akhir" placeholder="Tanggal Akhir">
                        </div>
                        <div class="col-lg-2 col-sm-12 col-md-4 col-xxl-2 mb-2 mb-lg-0">
                            <button type="button" wire:click="resetFilters" class="btn btn-light">
                                <i class="fas fa-sync"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table mb-0">
                        <thead>
                            <tr>
                                <th width="50">No.</th>
                                <th>Nama Siswa</th>
                                <th>Jenis Cuti</th>
                                <th>Tanggal</th>
                                <th>Alasan</th>
                                <th>Foto</th>
                                <th>Status</th>
                                <th>Catatan</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php $no = ($data_cuti->currentPage() - 1) * $data_cuti->perPage() + 1; @endphp
                            @forelse($data_cuti as $dc)
                            <tr>
                                <td>{{ $no++ }}</td>
                                <td>{{ $dc->User->name ?? '-' }}</td>
                                <td>{{ $dc->nama_cuti }}</td>
                                <td>{{ date('d-m-Y', strtotime($dc->tanggal)) }}</td>
                                <td>{{ Str::limit($dc->alasan_cuti, 50) }}</td>
                                <td>
                                    @if($dc->foto_cuti)
                                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal"
                                        data-bs-target="#fotoCuti{{ $dc->id }}">
                                        <i class="fas fa-image"></i>
                                    </button>
                                    @else
                                    -
                                    @endif
                                </td>
                                <td>
                                    @php
                                    $statusClass = match($dc->status_cuti) {
                                    'Diterima' => 'bg-success',
                                    'Pending' => 'bg-warning',
                                    'Ditolak' => 'bg-danger',
                                    default => 'bg-secondary'
                                    };
                                    @endphp
                                    <span class="badge {{ $statusClass }}">{{ $dc->status_cuti }}</span>
                                </td>
                                <td>{{ $dc->catatan ?? '-' }}</td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-light dropdown-toggle" type="button"
                                            data-bs-toggle="dropdown">
                                            Aksi
                                        </button>
                                        <ul class="dropdown-menu">
                                            @if($dc->status_cuti === 'Pending')
                                            <li>
                                                <button class="dropdown-item"
                                                    wire:click="openApprovalModal({{ $dc->id }})">
                                                    <i class="fas fa-check"></i> Proses
                                                </button>
                                            </li>
                                            @endif
                                            <li><a class="dropdown-item"
                                                    href="{{ url('/data-cuti/edit/'.$dc->id) }}">Edit</a></li>
                                            <li>
                                                <button class="dropdown-item text-danger"
                                                    onclick="confirm('Yakin ingin menghapus data ini?') || event.stopImmediatePropagation()"
                                                    wire:click="deleteLeave({{ $dc->id }})">
                                                    Hapus
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="9" class="text-center">Tidak ada data</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <div class="card-footer">
                    {{ $data_cuti->links() }}
                </div>
            </div>
        </div>
    </div>

    @if (session()->has('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    @endif

    <!-- Approval Modal -->
    @if($showModal && $selectedLeave)
    <div class="modal fade show" style="display: block;" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Proses Pengajuan Cuti</h5>
                    <button type="button" class="btn-close" wire:click="closeModal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <strong>Nama:</strong> {{ $selectedLeave->User->name }}
                    </div>
                    <div class="mb-3">
                        <strong>Jenis Cuti:</strong> {{ $selectedLeave->nama_cuti }}
                    </div>
                    <div class="mb-3">
                        <strong>Tanggal:</strong> {{ date('d-m-Y', strtotime($selectedLeave->tanggal)) }}
                    </div>
                    <div class="mb-3">
                        <strong>Alasan:</strong> {{ $selectedLeave->alasan_cuti }}
                    </div>
                    @if($selectedLeave->foto_cuti)
                    <div class="mb-3">
                        <strong>Foto:</strong><br>
                        <img src="{{ url('storage/'.$selectedLeave->foto_cuti) }}" class="img-fluid"
                            style="max-height: 200px;" alt="Foto Cuti">
                    </div>
                    @endif

                    <div class="mb-3">
                        <label class="form-label">Status Approval</label>
                        <select wire:model="approval_status" class="form-control">
                            <option value="">Pilih Status</option>
                            <option value="Diterima">Diterima</option>
                            <option value="Ditolak">Ditolak</option>
                        </select>
                        @error('approval_status') <div class="text-danger">{{ $message }}</div> @enderror
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Catatan</label>
                        <textarea wire:model="catatan" class="form-control" rows="3"
                            placeholder="Catatan (opsional)"></textarea>
                        @error('catatan') <div class="text-danger">{{ $message }}</div> @enderror
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModal">Batal</button>
                    <button type="button" class="btn btn-primary" wire:click="processApproval">Simpan</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
    @endif
</div>