<?php

namespace App\Http\Controllers;

use Spatie\Image\Image;
use Illuminate\Http\Request;

class AaController extends Controller
{
    //
    public function index()
    {
        return view('aa.index', [
            'title' => 'Tambah Izin'
        ]);
    }

    public function store(Request $request)
    {
        if ($request->file('foto_cuti')) {
            $filename = $request->file('foto_cuti')->hashName();
            Image::load($request->file('foto_cuti'))
                ->optimize()
                ->width(400)
                ->save(public_path('storage/aa/') . $filename);
            dd('aa/' . $filename);
        }
    }
}
