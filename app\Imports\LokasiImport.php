<?php

namespace App\Imports;

use App\Models\Lokasi;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class LokasiImport implements ToModel, WithHeadingRow
{
    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        // dd($row["nama_lokasi"]);
        return new Lokasi([
            "nama_lokasi" => $row["nama_lokasi"],
            "lat_kantor" => $row["lat_kantor"],
            "long_kantor" => $row["long_kantor"],
            "radius" => $row["radius"],
            "jurusan" => $row["jurusan"],
            "status" => $row["status"],
            "created_by" => $row["created_by"],
        ]);

        // return new Lokasi([
        //     "nama_lokasi" => "nama_lokasi",
        //     "lat_kantor" => 0,
        //     "long_kantor" => 0,
        //     "radius" => 100,
        //     "status" => "approved",
        //     "created_by" => 1,
        // ]);
    }
}
