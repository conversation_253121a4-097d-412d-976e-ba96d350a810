<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Jabatan;
use App\Models\Jurusan;
use Illuminate\Http\Request;
use RealRashid\SweetAlert\Facades\Alert;

class JurusanController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $search = request()->input('search');
        $jurusan = Jurusan::when($search, function ($query) use ($search) {
            $query->where('nama_jurusan', 'LIKE', '%' . $search . '%');
        })
            ->orderBy('nama_jurusan', 'ASC')
            ->paginate(10)
            ->withQueryString();

        return view('jurusan.index', [
            'title' => 'Jurusan',
            'data_jurusan' => $jurusan
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('jurusan.create', [
            'title' => 'Tambah Data Jurusan',
            // 'users' => User::select('id', 'name')->where('is_admin', 'admin')->orderBy('name')->get(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'kode_jurusan' => 'required|max:10',
            'nama_jurusan' => 'required|max:100',
            'nama_kajur' => 'required|max:100',
        ]);

        Jurusan::create($validatedData);
        return redirect('/jurusan')->with('success', 'Data Berhasil di Tambahkan');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        return view('jurusan.edit', [
            'title' => 'Edit Data Jurusan',
            'data_jurusan' => Jurusan::findOrFail($id),
            // 'users' => User::select('id', 'name')->where('is_admin', 'admin')->orderBy('name')->get(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'kode_jurusan' => 'required|max:10',
            'nama_jurusan' => 'required|max:100',
            'nama_kajur' => 'required|max:100',
        ]);

        Jurusan::whereId($id)->update($validatedData);
        return redirect('/jurusan')->with('success', 'Data Berhasil di Update');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function delete($id)
    {
        $jurusan = Jurusan::findOrFail($id);
        $kelas = Jabatan::where('jurusan_id', $id)->count();
        $siswa = User::where('jurusan_id', $id)->count();

        if ($kelas > 0 || $siswa > 0) {
            Alert::error('Failed', 'Masih Ada Kelas/ User Yang Menggunakan Jurusan Ini!');
            return redirect('/jurusan');
        } else {
            $jurusan->delete();
            return redirect('/jurusan')->with('success', 'Data Berhasil di Delete');
        }
    }
}
