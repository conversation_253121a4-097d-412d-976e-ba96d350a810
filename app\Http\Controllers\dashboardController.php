<?php

namespace App\Http\Controllers;

use App\Models\Cuti;
use App\Models\User;
use App\Models\Lembur;
use App\Models\Lokasi;
use App\Models\settings;
use App\Models\ResetCuti;
use App\Models\PilihJadwal;
use App\Models\SurveyShift;
use App\Models\MappingShift;
use App\Models\SurveyLokasi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class dashboardController extends Controller
{
    public function index()
    {
        date_default_timezone_set('Asia/Jakarta');

        $settings = settings::first();

        $tgl_skrg = date("Y-m-d");

        $wkt_skrg = date("Y-m-d H:i:s");
        DB::update('UPDATE users SET last_login = ? WHERE id = ?', [$wkt_skrg, auth()->user()->id]);

        if (auth()->user()->is_admin == "admin") {
            return view('dashboard.livewire-index', [
                'title' => 'Dashboard',
            ]);
        } else if (auth()->user()->is_admin == "guru") {
            $user_login = auth()->user()->id;
            // $tanggal = "";
            $tglskrg = date('Y-m-d');
            // $tglkmrn = date('Y-m-d', strtotime('-1 days'));
            // $mapping_shift = MappingShift::select('jam_absen', 'jam_pulang')->where('user_id', $user_login)->where('tanggal', $tglkmrn)->get();
            // if ($mapping_shift->count() > 0) {
            //     foreach ($mapping_shift as $mp) {
            //         $jam_absen = $mp->jam_absen;
            //         $jam_pulang = $mp->jam_pulang;
            //     }
            // } else {
            //     $jam_absen = "-";
            //     $jam_pulang = "-";
            // }
            // if ($jam_absen != null && $jam_pulang == null) {
            //     $tanggal = $tglkmrn;
            // } else {
            //     $tanggal = $tglskrg;
            // }

            $jumlah_izin = Cuti::select('id')->whereRaw(DB::raw('user_id IN (select id from users where pembimbing_id = ' . auth()->user()->id . ')'))->where('status_cuti', 'Pending')->count();
            $jumlah_pa = MappingShift::select('id')->whereRaw(DB::raw('user_id IN (select id from users where pembimbing_id = ' . auth()->user()->id . ')'))->where('status_pengajuan', 'Menunggu')->count();
            $belum_isi_dudi = User::select('id', 'name')->whereRaw(DB::raw('id NOT IN(SELECT user_id FROM survey_lokasis) AND is_admin = "user" AND pembimbing_id = ' . auth()->user()->id . ''))->get();

            // '.$tglskrg.'
            $belum_ada_jadwal = User::select('name')->whereRaw(DB::raw('pembimbing_id = ' . auth()->user()->id . '
                AND is_admin = "user"
                AND id NOT IN(SELECT user_id FROM mapping_shifts WHERE tanggal = "' . $tglskrg . '")
                ORDER BY lokasi_id ASC, name ASC'))
                ->get();

            $pengumuman = DB::select(DB::raw('SELECT id, tanggal_mulai, tanggal_akhir, kepada, pesan, sifat FROM pengumumans WHERE tanggal_mulai <= "' . $tglskrg . '" AND tanggal_akhir >= "' . $tglskrg . '" AND kepada = "guru" AND status = "1"'));

            return view('dashboard.indexGuru', [
                'title' => 'Dashboard',
                // 'shift_karyawan' => MappingShift::where('user_id', $user_login)->where('tanggal', $tanggal)->first(),
                'jumlah_izin' => $jumlah_izin,
                'jumlah_pa' => $jumlah_pa,
                'belum_isi_dudi' => $belum_isi_dudi,
                'belum_ada_jadwal' => $belum_ada_jadwal,
                'pengumuman' => $pengumuman,
            ]);
        } else {
            $user_login = auth()->user()->id;
            $tanggal = date('Y-m-d');
            $tglkmrn = date('Y-m-d', strtotime('-1 days'));

            //survey lokasi
            $survey = SurveyLokasi::select('id')->where('user_id', $user_login)->count();

            if ($settings->survey_lokasi == 1) {
                $sudah_absen_pulang = MappingShift::where('user_id', $user_login)->where('tanggal', $tanggal)->whereNotNull('jam_pulang')->count();
                if (!$survey && $sudah_absen_pulang) {
                    return redirect('/survey-lokasi');
                }
            }

            //survey shift
            $survey_shift = auth()->user()->SurveyShift->count();

            if (!$survey_shift && $settings->survey_shift == 1) {
                return redirect('/survey-shift');
            }

            //sementara off kan dulu -- jika sudah pilih jadwal
            // $date = new \DateTime();
            // $date->modify('monday this week');
            // $tanggal_mulai =  $date->format('Y-m-d');

            // $date->modify('sunday this week');
            // $tanggal_akhir =  $date->format('Y-m-d');

            // $sudah_isi = PilihJadwal::where('user_id', auth()->user()->id)->where('tanggal_mulai', $tanggal_mulai)->where('tanggal_akhir', $tanggal_akhir)->count();

            //------------------sementara off kan dulu -- jika belum pilih jadwal
            // $approved = SurveyShift::where('lokasi_id', auth()->user()->Lokasi->id)->where('approved', 1)->count();

            // if ($approved && !$sudah_isi) {
            //     return redirect('/jadwal/pilih');
            // }

            //dd($approved . '--' . $sudah_isi);

            // $tanggal = "";
            // $tglskrg = date('Y-m-d');
            // $tglkmrn = date('Y-m-d', strtotime('-1 days'));
            // $mapping_shift = MappingShift::where('user_id', $user_login)->where('tanggal', $tglkmrn)->get();
            // if ($mapping_shift->count() > 0) {
            //     foreach ($mapping_shift as $mp) {
            //         $jam_absen = $mp->jam_absen;
            //         $jam_pulang = $mp->jam_pulang;
            //     }
            // } else {
            //     $jam_absen = "-";
            //     $jam_pulang = "-";
            // }
            // if ($jam_absen != null && $jam_pulang == null) {
            //     $tanggal = $tglkmrn;
            // } else {
            //     $tanggal = $tglskrg;
            // }

            // $ada_jadwal = MappingShift::select('id')->where('user_id', $user_login)->where('tanggal', $tanggal)->first();
            $ada_jadwal = DB::select(DB::raw('SELECT id FROM mapping_shifts WHERE user_id = ' . $user_login . ' AND tanggal = "' . $tanggal . '"'));

            // dd($ada_jadwal);
            if (!$ada_jadwal && ($settings->pilih_jadwal_mingguan == 1 || $settings->pilih_jadwal_reguler == 1)) {
                return redirect('/jadwal/pilih');
            }

            $pengumuman = DB::select(DB::raw('SELECT id, tanggal_mulai, tanggal_akhir, kepada, pesan, sifat FROM pengumumans WHERE tanggal_mulai <= "' . $tanggal . '" AND tanggal_akhir >= "' . $tanggal . '" AND kepada = "user" AND status = "1"'));

            return view('dashboard.indexUser', [
                'title' => 'Dashboard',
                // 'approved' => $approved,
                'survey' => $survey,
                'settings' => $settings,
                'shift_karyawan' => MappingShift::select('jam_masuk', 'jam_keluar')->where('user_id', $user_login)->where('tanggal', $tanggal)->first(),
                'pengumuman' => $pengumuman,
                'kemarin' => MappingShift::select('id', 'tanggal', 'jam_pulang')
                    ->where('user_id', $user_login)
                    ->where('tanggal', $tglkmrn)
                    ->where('status_absen', '=', 'Tidak Masuk')
                    ->whereNull('jam_pulang')
                    ->count(),
            ]);
        }
    }

    public function tools()
    {
        return view('dashboard.tools', [
            'title' => 'Tools',
            'lokasi' => Lokasi::select('id', 'nama_lokasi')->get(),
        ]);
    }

    public function menu()
    {
        return view('dashboard.menu', [
            'title' => 'All Menu',
        ]);
    }

    public function chartData()
    {
        date_default_timezone_set('Asia/Jakarta');
        // dd(request()->all());
        $mulai = request('mulai') ? request('mulai') : date('Y-m-d', strtotime('monday this week'));
        $akhir = request('akhir') ? request('akhir') : date('Y-m-d');

        $info_absen = DB::select(DB::raw('SELECT DATE_FORMAT(tanggal, "%b-%e") as tanggal_short, count(tanggal) as terjadwal,
                COUNT(CASE WHEN status_absen = "Masuk" AND jam_absen IS NOT NULL THEN 1 ELSE NULL END) AS masuk,
                COUNT(CASE WHEN status_absen = "Masuk" AND jam_pulang IS NOT NULL THEN 1 ELSE NULL END) AS pulang,
                COUNT(IF(status_absen = "Libur", 1, NULL)) as libur,
                COUNT(IF(status_absen = "Cuti", 1, NULL)) as cuti,
                COUNT(IF(status_absen = "Izin Sakit", 1, NULL)) as izin_sakit,
                COUNT(IF(status_absen = "Izin Tidak Masuk", 1, NULL)) as izin_tidak_masuk,
                COUNT(IF(status_absen = "Izin Telat", 1, NULL)) as izin_telat,
                COUNT(IF(status_absen = "Izin Pulang Cepat", 1, NULL)) as izin_pulang_cepat,
                COUNT(IF(status_absen = "Tidak Masuk", 1, NULL)) as alpha
                FROM mapping_shifts WHERE tanggal >= "' . $mulai . '" AND tanggal <= "' . $akhir . '"
                GROUP BY tanggal
                ORDER BY tanggal ASC'));
        return response()->json($info_absen);
    }
}
