<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PilihJadwal extends Model
{
    use HasFactory;
    protected $guarded = ['id'];
    public $timestamps = true;

    public function User()
    {
        return $this->belongsTo(User::class);
    }

    public function MappingShift($user_id, $tanggal_awal, $tanggal_akhir)
    {
        return MappingShift::where('user_id', $user_id)->whereBetween('tanggal', [$tanggal_awal, $tanggal_akhir]);
    }
}
