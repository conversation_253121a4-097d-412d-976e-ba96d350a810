<?php

namespace App\Http\Livewire\Auth;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class LoginForm extends Component
{
    public $username = '';
    public $password = '';
    public $remember = false;
    public $showPassword = false;
    public $isLoading = false;

    protected $rules = [
        'username' => 'required|string',
        'password' => 'required|string',
    ];

    protected $messages = [
        'username.required' => 'Username wajib diisi.',
        'password.required' => 'Password wajib diisi.',
    ];

    public function togglePassword()
    {
        $this->showPassword = !$this->showPassword;
    }

    public function login()
    {
        $this->isLoading = true;

        $this->validate();

        // Check if user exists
        $user = User::where('username', $this->username)->first();

        if (!$user) {
            $this->addError('username', 'Username tidak ditemukan.');
            $this->isLoading = false;
            return;
        }

        // Check password
        if (!Hash::check($this->password, $user->password)) {
            $this->addError('password', 'Password salah.');
            $this->isLoading = false;
            return;
        }

        // Attempt login
        if (Auth::attempt(['username' => $this->username, 'password' => $this->password], $this->remember)) {
            // Update last login
            $user->update(['last_login' => now()]);

            session()->regenerate();

            // Redirect based on user role
            if ($user->is_admin === 'admin') {
                return redirect()->intended('/dashboard');
            } else {
                return redirect()->intended('/dashboard');
            }
        } else {
            $this->addError('username', 'Login gagal. Silakan coba lagi.');
        }

        $this->isLoading = false;
    }

    public function render()
    {
        return view('livewire.auth.login-form');
    }
}
