<?php

namespace App\Http\Livewire\Attendance;

use App\Models\User;
use App\Models\Lokasi;
use App\Models\Jabatan;
use App\Models\MappingShift;
use Livewire\Component;
use Livewire\WithPagination;

class AttendanceData extends Component
{
    use WithPagination;

    public $user_id = '';
    public $jabatan_id = '';
    public $lokasi_id = '';
    public $mulai = '';
    public $akhir = '';
    public $perPage = 20;

    protected $queryString = [
        'user_id' => ['except' => ''],
        'jabatan_id' => ['except' => ''],
        'lokasi_id' => ['except' => ''],
        'mulai' => ['except' => ''],
        'akhir' => ['except' => ''],
    ];

    public function mount()
    {
        date_default_timezone_set('Asia/Jakarta');
        if (!$this->mulai) {
            $this->mulai = date('Y-m-d', strtotime('-7 days'));
        }
        if (!$this->akhir) {
            $this->akhir = date('Y-m-d');
        }
    }

    public function updatingUserId()
    {
        $this->resetPage();
    }

    public function updatingJabatanId()
    {
        $this->resetPage();
    }

    public function updatingLokasiId()
    {
        $this->resetPage();
    }

    public function updatingMulai()
    {
        $this->resetPage();
    }

    public function updatingAkhir()
    {
        $this->resetPage();
    }

    public function resetFilters()
    {
        $this->user_id = '';
        $this->jabatan_id = '';
        $this->lokasi_id = '';
        $this->mulai = date('Y-m-d', strtotime('-7 days'));
        $this->akhir = date('Y-m-d');
        $this->resetPage();
    }

    public function render()
    {
        // Get filter data
        $users = User::select('id', 'name')->where('is_admin', 'user')->orderBy('name')->get();
        $jabatan = Jabatan::select('id', 'nama_jabatan')->where('id', '!=', '1')->orderBy('nama_jabatan')->get();
        $lokasi = Lokasi::select('id', 'nama_lokasi')->orderBy('nama_lokasi')->get();

        // Build query for attendance data
        $query = MappingShift::with(['User.Jabatan', 'User.Lokasi', 'Shift'])
            ->whereHas('User', function ($q) {
                $q->where('is_admin', 'user');
            });

        // Apply filters
        if ($this->user_id) {
            $query->where('user_id', $this->user_id);
        }

        if ($this->jabatan_id) {
            $query->whereHas('User', function ($q) {
                $q->where('jabatan_id', $this->jabatan_id);
            });
        }

        if ($this->lokasi_id) {
            $query->whereHas('User', function ($q) {
                $q->where('lokasi_id', $this->lokasi_id);
            });
        }

        if ($this->mulai) {
            $query->where('tanggal', '>=', $this->mulai);
        }

        if ($this->akhir) {
            $query->where('tanggal', '<=', $this->akhir);
        }

        $data_absen = $query->orderBy('tanggal', 'desc')
            ->orderBy('user_id')
            ->paginate($this->perPage);

        return view('livewire.attendance.attendance-data', [
            'data_absen' => $data_absen,
            'users' => $users,
            'jabatan' => $jabatan,
            'lokasi' => $lokasi,
        ]);
    }
}
