<?php

namespace App\Http\Controllers;

use App\Exports\DudiExport;
use App\Models\SurveyLokasi;
use Illuminate\Http\Request;

class SurveyLokasiController extends Controller
{
    //digunakan untuk melengkapi data lokasi yang dilakukan mandiri oleh siswa (survey lokasi)
    public function index()
    {
        return view('surveylokasi.index', [
            'title' => 'Data Lokasi PKL',
        ]);
    }

    public function simpan(Request $request)
    {
        $request->validate([
            'user_id' => 'required',
            'lokasi_id' => 'required',
            'nama_dudi' => 'required',
            // 'nib' => 'required',
            'unit_kerja' => 'required',
            'jalan' => 'required',
            'dusun' => 'required',
            'desa' => 'required',
            'kecamatan' => 'required',
            'kabupaten' => 'required',
            'telepon' => 'required',
            // 'email' => 'required|email:dns',
            // 'website' => 'required',
            // 'npwp' => 'required',
            'pimpinan_nama' => 'required',
            // 'pimpinan_nip' => 'required',
            'pimpinan_jabatan' => 'required',
            'pembimbing_nama' => 'required',
            // 'pembimbing_nip' => 'required',
            'pembimbing_jabatan' => 'required',
            'pembimbing_telepon' => 'required',
            // 'pembimbing_email' => 'required|email:dns',
        ]);

        $alamat = [
            'jalan' => $request->jalan,
            'dusun' => $request->dusun,
            'desa' => $request->desa,
            'kecamatan' => $request->kecamatan,
            'kabupaten' => $request->kabupaten,
        ];

        $alamat = json_encode($alamat);

        $pimpinan = [
            'nama' => $request->pimpinan_nama,
            'nip' => $request->pimpinan_nip,
            'jabatan' => $request->pimpinan_jabatan,
        ];

        $pimpinan = json_encode($pimpinan);

        $pembimbing = [
            'nama' => $request->pembimbing_nama,
            'nip' => $request->pembimbing_nip,
            'jabatan' => $request->pembimbing_jabatan,
            'telepon' => $request->pembimbing_telepon,
            'email' => $request->pembimbing_email,
        ];

        $pembimbing = json_encode($pembimbing);

        $data = [
            'user_id' => $request->user_id,
            'lokasi_id' => $request->lokasi_id,
            'nama_dudi' => $request->nama_dudi,
            'nib' => $request->nib,
            'unit_kerja' => $request->unit_kerja,
            'alamat' => $alamat,
            'telepon' => $request->telepon,
            'email' => $request->email,
            'website' => $request->website,
            'npwp' => $request->npwp,
            'pimpinan' => $pimpinan,
            'pembimbing' => $pembimbing,
        ];

        SurveyLokasi::create($data);
        return redirect('/dashboard')->with('success', 'Terima kasih Anda telah mengisi data lokasi PKL.');
    }

    public function edit()
    {
        $survey = SurveyLokasi::where('user_id', auth()->user()->id)->first();
        return view('surveylokasi.edit', [
            'title' => 'Edit Data Lokasi PKL',
            'survey' => $survey
        ]);
    }

    public function editAdmin($id)
    {
        $survey = SurveyLokasi::findOrFail($id);
        return view('surveylokasi.editadmin', [
            'title' => 'Edit Data Lokasi PKL',
            'survey' => $survey
        ]);
    }

    public function updateAdmin(Request $request, $id)
    {
        $validatedData = $request->validate([
            'nama_dudi' => 'required',
            // 'nib' => 'required',
            'unit_kerja' => 'required',
            'jalan' => 'required',
            'dusun' => 'required',
            'desa' => 'required',
            'kecamatan' => 'required',
            'kabupaten' => 'required',
            'telepon' => 'required',
            // 'email' => 'required|email:dns',
            // 'website' => 'required',
            // 'npwp' => 'required',
            'pimpinan_nama' => 'required',
            'pimpinan_jabatan' => 'required',
            'pembimbing_nama' => 'required',
            'pembimbing_jabatan' => 'required',
            'pembimbing_telepon' => 'required',
            // 'pembimbing_email' => 'required|email:dns',
        ]);

        $alamat = [
            'jalan' => $request->jalan,
            'dusun' => $request->dusun,
            'desa' => $request->desa,
            'kecamatan' => $request->kecamatan,
            'kabupaten' => $request->kabupaten,
        ];

        $alamat = json_encode($alamat);

        $pimpinan = [
            'nama' => $request->pimpinan_nama,
            'nip' => $request->pimpinan_nip,
            'jabatan' => $request->pimpinan_jabatan,
        ];

        $pimpinan = json_encode($pimpinan);

        $pembimbing = [
            'nama' => $request->pembimbing_nama,
            'nip' => $request->pembimbing_nip,
            'jabatan' => $request->pembimbing_jabatan,
            'telepon' => $request->pembimbing_telepon,
            'email' => $request->pembimbing_email,
        ];

        $pembimbing = json_encode($pembimbing);

        $data = [
            'nama_dudi' => $request->nama_dudi,
            'nib' => $request->nib,
            'unit_kerja' => $request->unit_kerja,
            'alamat' => $alamat,
            'telepon' => $request->telepon,
            'email' => $request->email,
            'website' => $request->website,
            'npwp' => $request->npwp,
            'pimpinan' => $pimpinan,
            'pembimbing' => $pembimbing,
        ];

        SurveyLokasi::where('id', $id)->update($data);
        return redirect('/survey-lokasi/data')->with('success', 'Data Lokasi Berhasil Diperbahui');
    }

    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'nama_dudi' => 'required',
            // 'nib' => 'required',
            'unit_kerja' => 'required',
            'jalan' => 'required',
            'dusun' => 'required',
            'desa' => 'required',
            'kecamatan' => 'required',
            'kabupaten' => 'required',
            'telepon' => 'required',
            'email' => 'required|email:dns',
            // 'website' => 'required',
            // 'npwp' => 'required',
            'pimpinan_nama' => 'required',
            'pimpinan_jabatan' => 'required',
            'pembimbing_nama' => 'required',
            'pembimbing_jabatan' => 'required',
            'pembimbing_telepon' => 'required',
            'pembimbing_email' => 'required|email:dns',
        ]);

        $alamat = [
            'jalan' => $request->jalan,
            'dusun' => $request->dusun,
            'desa' => $request->desa,
            'kecamatan' => $request->kecamatan,
            'kabupaten' => $request->kabupaten,
        ];

        $alamat = json_encode($alamat);

        $pimpinan = [
            'nama' => $request->pimpinan_nama,
            'nip' => $request->pimpinan_nip,
            'jabatan' => $request->pimpinan_jabatan,
        ];

        $pimpinan = json_encode($pimpinan);

        $pembimbing = [
            'nama' => $request->pembimbing_nama,
            'nip' => $request->pembimbing_nip,
            'jabatan' => $request->pembimbing_jabatan,
            'telepon' => $request->pembimbing_telepon,
            'email' => $request->pembimbing_email,
        ];

        $pembimbing = json_encode($pembimbing);

        $data = [
            'nama_dudi' => $request->nama_dudi,
            'nib' => $request->nib,
            'unit_kerja' => $request->unit_kerja,
            'alamat' => $alamat,
            'telepon' => $request->telepon,
            'email' => $request->email,
            'website' => $request->website,
            'npwp' => $request->npwp,
            'pimpinan' => $pimpinan,
            'pembimbing' => $pembimbing,
        ];

        SurveyLokasi::where('id', $id)->update($data);
        return redirect('/dashboard')->with('success', 'Data Lokasi Berhasil Diperbahui');
    }

    public function data()
    {
        $search = request()->input('search');

        $survey = SurveyLokasi::when($search, function ($query) use ($search) {
            $query->whereHas('User', function ($query) use ($search) {
                $query->where('name', 'LIKE', '%' . $search . '%');
            })
                ->orwhereHas('Lokasi', function ($query) use ($search) {
                    $query->where('nama_lokasi', 'LIKE', '%' . $search . '%');
                });
        })
            ->orderBy('lokasi_id', 'ASC')->orderBy('user_id', 'ASC')
            ->paginate(20)
            ->withQueryString();

        return view('surveylokasi.data', [
            'title' => 'Data Lokasi PKL',
            'survey' => $survey
        ]);
    }

    public function approve($id)
    {
        SurveyLokasi::findOrFail($id)->update(['approved' => '1']);
        return redirect('/survey-lokasi/data')->with('success', 'Data Lokasi Berhasil Disetujui');
    }

    public function exportDataLokasi()
    {
        return (new DudiExport($_GET))->download('Data_Lokasi_PKL.xlsx');
    }
}
