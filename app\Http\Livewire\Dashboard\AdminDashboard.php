<?php

namespace App\Http\Livewire\Dashboard;

use App\Models\User;
use Livewire\Component;
use Illuminate\Support\Facades\DB;

class AdminDashboard extends Component
{
    public $mulai;
    public $akhir;
    public $info_absen = [];
    public $info_pengajuan = [];
    public $jumlah_user = 0;
    public $jumlah_belum_mapping = 0;

    public function mount()
    {
        $this->mulai = request('mulai') ?: date('Y-m-d', strtotime('monday this week'));
        $this->akhir = request('akhir') ?: date('Y-m-d');
        $this->loadData();
    }

    public function updatedMulai()
    {
        $this->loadData();
        $this->emit('dataUpdated');
    }

    public function updatedAkhir()
    {
        $this->loadData();
        $this->emit('dataUpdated');
    }

    public function loadData()
    {
        date_default_timezone_set('Asia/Jakarta');

        // Update last login
        $wkt_skrg = date("Y-m-d H:i:s");
        DB::update('UPDATE users SET last_login = ? WHERE id = ?', [$wkt_skrg, auth()->user()->id]);

        // Get attendance info
        $this->info_absen = DB::select(DB::raw('SELECT tanggal, count(tanggal) as terjadwal,
            COUNT(CASE WHEN status_absen = "Masuk" AND jam_absen IS NOT NULL THEN 1 ELSE NULL END) AS masuk,
            COUNT(CASE WHEN status_absen = "Masuk" AND jam_pulang IS NOT NULL THEN 1 ELSE NULL END) AS pulang,
            COUNT(IF(status_absen = "Libur", 1, NULL)) as libur,
            COUNT(IF(status_absen = "Cuti", 1, NULL)) as cuti,
            COUNT(IF(status_absen = "Izin Sakit", 1, NULL)) as izin_sakit,
            COUNT(IF(status_absen = "Izin Tidak Masuk", 1, NULL)) as izin_tidak_masuk,
            COUNT(IF(status_absen = "Izin Telat", 1, NULL)) as izin_telat,
            COUNT(IF(status_absen = "Izin Pulang Cepat", 1, NULL)) as izin_pulang_cepat,
            COUNT(IF(status_absen = "Tidak Masuk", 1, NULL)) as alpha
            FROM mapping_shifts WHERE tanggal >= "' . $this->mulai . '" AND tanggal <= "' . $this->akhir . '"
            GROUP BY tanggal
            ORDER BY tanggal ASC'));

        // Get pending requests info
        $info_pengajuan = DB::select(DB::raw('SELECT tanggal,
            COUNT(IF(status_cuti = "Pending", 1, NULL)) as pengajuan FROM cutis
            WHERE tanggal >= "' . $this->mulai . '" AND tanggal <= "' . $this->akhir . '"
            GROUP BY tanggal
            ORDER BY tanggal ASC'));

        $this->info_pengajuan = [];
        foreach ($info_pengajuan as $ip) {
            $this->info_pengajuan[$ip->tanggal] = $ip->pengajuan;
        }

        // Get user counts
        $this->jumlah_user = User::select('id')->where('is_admin', 'user')->count();
        $this->jumlah_belum_mapping = User::select('id')->where('is_admin', 'user')->whereNull('pembimbing_id')->count();
    }

    public function render()
    {
        return view('livewire.dashboard.admin-dashboard');
    }
}
