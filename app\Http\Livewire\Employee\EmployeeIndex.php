<?php

namespace App\Http\Livewire\Employee;

use App\Models\User;
use App\Models\Jabatan;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class EmployeeIndex extends Component
{
    use WithPagination;

    public $search = '';
    public $pembimbing = '';
    public $login = '';
    public $perPage = 20;

    protected $queryString = [
        'search' => ['except' => ''],
        'pembimbing' => ['except' => ''],
        'login' => ['except' => ''],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingPembimbing()
    {
        $this->resetPage();
    }

    public function updatingLogin()
    {
        $this->resetPage();
    }

    public function resetFilters()
    {
        $this->search = '';
        $this->pembimbing = '';
        $this->login = '';
        $this->resetPage();
    }

    public function deleteEmployee($id)
    {
        $employee = User::find($id);
        if ($employee) {
            // Delete related records
            $employee->mappingShifts()->delete();
            $employee->lemburs()->delete();
            $employee->cutis()->delete();
            $employee->payrolls()->delete();

            // Delete photo if exists
            if ($employee->foto_karyawan) {
                Storage::delete($employee->foto_karyawan);
            }

            // Update neural.json file
            $path = public_path('neural.json');
            if (file_exists($path)) {
                $neural = File::get($path);
                $dataface = json_decode($neural, true);

                $filterface = array_filter($dataface, function ($item) use ($employee) {
                    return $item['label'] !== $employee->username;
                });
                File::put($path, json_encode(array_values($filterface), JSON_PRETTY_PRINT));
            }

            $employee->delete();
            session()->flash('success', 'Data Berhasil di Delete');
        }
    }

    public function render()
    {
        $data = User::when($this->search, function ($query) {
            $query->where('name', 'LIKE', '%' . $this->search . '%')
                ->orWhere('email', 'LIKE', '%' . $this->search . '%')
                ->orWhere('telepon', 'LIKE', '%' . $this->search . '%')
                ->orWhere('username', 'LIKE', '%' . $this->search . '%')
                ->orWhereHas('Jabatan', function ($query) {
                    $query->where('nama_jabatan', 'LIKE', '%' . $this->search . '%');
                })
                ->orWhereHas('Lokasi', function ($query) {
                    $query->where('nama_lokasi', 'LIKE', '%' . $this->search . '%');
                });
        })->when($this->pembimbing, function ($query) {
            $query->whereNull('pembimbing_id');
        })->when($this->login, function ($query) {
            $query->whereNull('last_login');
        })
            ->where('is_admin', 'user')
            ->orderBy('name', 'ASC')
            ->paginate($this->perPage);

        $jabatan = Jabatan::select('id', 'nama_jabatan')->where('id', '!=', '1')->get();

        return view('livewire.employee.employee-index', [
            'data_user' => $data,
            'jabatan' => $jabatan,
        ]);
    }
}
