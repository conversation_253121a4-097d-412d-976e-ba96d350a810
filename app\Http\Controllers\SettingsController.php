<?php

namespace App\Http\Controllers;

use App\Models\settings;
use Illuminate\Http\Request;

class SettingsController extends Controller
{
    public function index()
    {
        $title = 'Settings';
        $data = settings::first();
        return view('settings.index', compact(
            'title',
            'data'
        ));
    }

    public function store(Request $request)
    {
        $settings = settings::first();

        $validated = $request->validate([
            'name' => 'required',
            'first_name' => 'required',
            'last_name' => 'required',
            'logo' => 'image|file|max:10240|nullable',
            'alamat' => 'nullable',
            'phone' => 'nullable',
            'email' => 'nullable',
        ]);
        if ($request->file('logo')) {
            $validated['logo'] = $request->file('logo')->store('logo');
        }
        $settings->update($validated);
        return back()->with('success', 'Data berhasil diubah');
    }

    public function storeApp(Request $request)
    {
        $settings = settings::first();
        // dd($request);
        $validated = $request->validate([
            'pilih_jadwal_mingguan' => 'nullable',
            'pilih_jadwal_reguler' => 'nullable',
            'survey_shift' => 'nullable',
            'survey_lokasi' => 'nullable',
            'pengajuan_absensi' => 'nullable',
            'pengajuan_absensi_hari' => 'nullable',
        ]);

        $validated['pilih_jadwal_mingguan'] = $request->pilih_jadwal_mingguan ?? 0;
        $validated['pilih_jadwal_reguler'] = $request->pilih_jadwal_reguler ?? 0;
        $validated['survey_shift'] = $request->survey_shift ?? 0;
        $validated['survey_lokasi'] = $request->survey_lokasi ?? 0;
        $validated['pengajuan_absensi'] = $request->pengajuan_absensi ?? 0;
        $validated['pengajuan_absensi_hari'] = $request->pengajuan_absensi_hari ?? 0;

        $settings->update($validated);
        return back()->with('success', 'Data berhasil diubah');
    }
}
