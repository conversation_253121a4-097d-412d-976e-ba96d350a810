@extends('templates.dashboard')
@section('isi')
    @livewire('dashboard.admin-dashboard')

    @push('script')
    <script src="{{ url('https://cdn.jsdelivr.net/npm/apexcharts@3.35.3/dist/apexcharts.min.js') }}"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Chart area
            var options = {
                chart: {
                    height: 328,
                    type: "area",
                    toolbar: {
                        show: false
                    },
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: "smooth",
                    width: 4
                },
                series: [],
                xaxis: {
                    type: "datetime",
                    categories: []
                },
                tooltip: {
                    x: {
                        format: "dd/MM/yy HH:mm"
                    }
                },
                colors: ["#198754", "#66DA26", "#0d6efd", "#6f42c1", "#FF9800", "#dc3545"],
                noData: {
                    text: 'Loading...'
                }
            };

            var chart = new ApexCharts(document.querySelector("#apexcharts-area"), options);
            chart.render();

            // Chart pie
            var optionspie = {
                chart: {
                    height: 328,
                    type: "pie",
                },
                labels: [],
                series: [],
                colors: ["#198754", "#0d6efd", "#6f42c1", "#FF9800", "#dc3545"],
                noData: {
                    text: 'Loading...'
                }
            };

            var chartpie = new ApexCharts(document.querySelector("#apexcharts-pie"), optionspie);
            chartpie.render();

            // Load chart data
            function loadChartData() {
                const mulai = @this.mulai;
                const akhir = @this.akhir;
                
                // Chart area
                $.ajax({
                    datatype: 'json',
                    url: "{{ url('/chart-data') }}",
                    method: 'GET',
                    data: {
                        "mulai": mulai,
                        "akhir": akhir
                    }
                }).done(async function(data) {
                    chart.updateOptions({
                        noData: {
                            text: ''
                        },
                        colors: ["#198754", "#66DA26", "#0d6efd", "#6f42c1", "#FF9800", "#dc3545"],
                        series: [
                        {
                            name: 'Masuk',
                            data: data.map(item => item.masuk)
                        },
                        {
                            name: 'Pulang',
                            data: data.map(item => item.pulang)
                        },
                        {
                            name: 'Libur',
                            data: data.map(item => item.libur)
                        },
                        {
                            name: 'Sakit',
                            data: data.map(item => item.izin_sakit)
                        },
                        {
                            name: 'Izin Tidak Masuk',
                            data: data.map(item => item.izin_tidak_masuk)
                        },
                        {
                            name: 'Alpha',
                            data: data.map(item => item.alpha)
                        }
                        ],
                        xaxis: {
                            categories: data.map(item => item.tanggal_short)
                        }
                    })
                });

                // Chart pie
                $.ajax({
                    datatype: 'json',
                    url: "{{ url('/chart-data') }}",
                    method: 'GET',
                    data: {
                        "mulai": "{{ date('Y-m-d') }}",
                        "akhir": "{{ date('Y-m-d') }}"
                    }
                }).done(async function(data1) {
                    chartpie.updateOptions({
                        noData: {
                            text: 'Loading...'
                        },
                        colors: ["#198754", "#0d6efd", "#6f42c1", "#FF9800", "#dc3545"],
                        series: [
                        data1[0].masuk,
                        data1[0].libur,
                        data1[0].izin_sakit,
                        data1[0].izin_tidak_masuk,
                        data1[0].alpha
                        ],
                        labels: [
                        'Masuk',
                        'Libur',
                        'Sakit',
                        'Izin Tidak Masuk',
                        'Alpha'
                        ]
                    })
                });
            }

            // Initial load
            loadChartData();

            // Listen for Livewire updates
            Livewire.on('dataUpdated', () => {
                loadChartData();
            });
        });
    </script>
    @endpush
@endsection
