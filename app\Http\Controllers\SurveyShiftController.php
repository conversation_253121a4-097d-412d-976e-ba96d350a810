<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Lokasi;
use App\Models\Jurusan;
use App\Models\PilihJadwal;
use App\Models\SurveyShift;
use App\Models\MappingShift;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use RealRashid\SweetAlert\Facades\Alert;

class SurveyShiftController extends Controller
{
    //
    public function create()
    {
        $survey = SurveyShift::where('user_id', auth()->user()->id)->count();
        if ($survey) {
            return redirect()->intended('/survey-shift/show');
        }

        // $awal = date('Y-m-d');
        // $akhir = date('Y-m-d', strtotime('+6 days'));

        // $hari = [];
        // for ($x = $awal; $x <= $akhir; $x = date('Y-m-d', strtotime($x . '+1 days'))) {
        //     $hari[] = date('D', strtotime($x));
        // }

        // dd($hari);

        return view('surveyshift.create', [
            'title' => 'Informasi Jadwal PKL',
        ]);
    }
    public function simpan(Request $request)
    {
        $request->validate([
            'user_id' => 'required',
            'lokasi_id' => 'required',
            'senin' => 'required',
            'senin_masuk' => 'required_if:senin,1',
            'senin_pulang' => 'required_if:senin,1',
            'selasa' => 'required',
            'selasa_masuk' => 'required_if:selasa,1',
            'selasa_pulang' => 'required_if:selasa,1',
            'rabu' => 'required',
            'rabu_masuk' => 'required_if:rabu,1',
            'rabu_pulang' => 'required_if:rabu,1',
            'kamis' => 'required',
            'kamis_masuk' => 'required_if:kamis,1',
            'kamis_pulang' => 'required_if:kamis,1',
            'jumat' => 'required',
            'jumat_masuk' => 'required_if:jumat,1',
            'jumat_pulang' => 'required_if:jumat,1',
            'sabtu' => 'required',
            'sabtu_masuk' => 'required_if:sabtu,1',
            'sabtu_pulang' => 'required_if:sabtu,1',
            'minggu' => 'required',
            'minggu_masuk' => 'required_if:minggu,1',
            'minggu_pulang' => 'required_if:minggu,1',
        ]);

        $jadwal = [
            'Mon' => ['jadwal' => $request->senin],
            'Tue' => ['jadwal' => $request->selasa],
            'Wed' => ['jadwal' => $request->rabu],
            'Thu' => ['jadwal' => $request->kamis],
            'Fri' => ['jadwal' => $request->jumat],
            'Sat' => ['jadwal' => $request->sabtu],
            'Sun' => ['jadwal' => $request->minggu],
        ];

        if ($request->senin == 1) {
            $jadwal['Mon']['masuk'] = $request->senin_masuk;
            $jadwal['Mon']['pulang'] = $request->senin_pulang;
        } else {
            $jadwal['Mon']['masuk'] = null;
            $jadwal['Mon']['pulang'] = null;
        }
        if ($request->selasa == 1) {
            $jadwal['Tue']['masuk'] = $request->selasa_masuk;
            $jadwal['Tue']['pulang'] = $request->selasa_pulang;
        } else {
            $jadwal['Tue']['masuk'] = null;
            $jadwal['Tue']['pulang'] = null;
        }
        if ($request->rabu == 1) {
            $jadwal['Wed']['masuk'] = $request->rabu_masuk;
            $jadwal['Wed']['pulang'] = $request->rabu_pulang;
        } else {
            $jadwal['Wed']['masuk'] = null;
            $jadwal['Wed']['pulang'] = null;
        }
        if ($request->kamis == 1) {
            $jadwal['Thu']['masuk'] = $request->kamis_masuk;
            $jadwal['Thu']['pulang'] = $request->kamis_pulang;
        } else {
            $jadwal['Thu']['masuk'] = null;
            $jadwal['Thu']['pulang'] = null;
        }
        if ($request->jumat == 1) {
            $jadwal['Fri']['masuk'] = $request->jumat_masuk;
            $jadwal['Fri']['pulang'] = $request->jumat_pulang;
        } else {
            $jadwal['Fri']['masuk'] = null;
            $jadwal['Fri']['pulang'] = null;
        }
        if ($request->sabtu == 1) {
            $jadwal['Sat']['masuk'] = $request->sabtu_masuk;
            $jadwal['Sat']['pulang'] = $request->sabtu_pulang;
        } else {
            $jadwal['Sat']['masuk'] = null;
            $jadwal['Sat']['pulang'] = null;
        }
        if ($request->minggu == 1) {
            $jadwal['Sun']['masuk'] = $request->minggu_masuk;
            $jadwal['Sun']['pulang'] = $request->minggu_pulang;
        } else {
            $jadwal['Sun']['masuk'] = null;
            $jadwal['Sun']['pulang'] = null;
        }
        $jadwal = json_encode($jadwal);

        $cek = SurveyShift::where('user_id', $request->user_id)->where('lokasi_id', $request->lokasi_id)->count();
        if ($cek) {
            return redirect()->intended('/survey-shift/show')->with('error', 'Anda sudah mengisi jadwal PKL di lokasi ini.');
        }

        SurveyShift::create([
            'user_id' => $request->user_id,
            'lokasi_id' => $request->lokasi_id,
            'jadwal' => $jadwal
        ]);
        // $insert = json_encode($jadwal);

        // dd($jadwal);
        return redirect('/dashboard')->with('success', 'Terimakasih Anda telah mengisi jadwal PKL.');
    }
    public function index()
    {
        //cek jika sudah ada jadwal minggu ini
        $date = new \DateTime();
        $date->modify('monday this week');
        $tanggal_mulai =  $date->format('Y-m-d');

        $date->modify('sunday this week');
        $tanggal_akhir =  $date->format('Y-m-d');

        // $jadwal_aktif = PilihJadwal::select('jadwal_id', 'hari_libur')->where('user_id', auth()->user()->id)->where('tanggal_mulai', $tanggal_mulai)->where('tanggal_akhir', $tanggal_akhir)->first();

        $jadwal_aktif = MappingShift::select('tanggal', 'jam_masuk', 'jam_keluar', 'status_absen')
            ->where('user_id', auth()->user()->id)
            ->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])
            ->orderBy('tanggal', 'ASC')
            ->get();

        $title = 'Usulan Jadwal PKL';
        $data = json_decode(SurveyShift::where('user_id', auth()->user()->id)->first());

        // dd($jadwal_aktif);
        $pilih_jadwal = 0;
        if ($jadwal_aktif->count() > 0) {
            $data = $jadwal_aktif;
            $pilih_jadwal = 1;
            $title = 'Jadwal PKL Minggu Ini';
        } else {
            $jadwal_aktif = null;
            // Alert::error('Error', 'Anda belum memilih jadwal PKL minggu ini.');
            // return redirect('/dashboard');
        }

        return view('surveyshift.index', [
            'title' => $title,
            'jadwal_aktif' => $jadwal_aktif,
            'pilih_jadwal' => $pilih_jadwal,
            'data' => $data,
        ]);
    }

    public function data()
    {
        $search = request()->input('search');
        $jurusan = request()->input('jurusan');
        $duplikat = request()->input('duplikat');
        $approved = request()->input('approved');
        $not_approved = request()->input('not_approved');

        $duplicates = SurveyShift::selectRaw('user_id')->groupBy('user_id')->havingRaw('COUNT(user_id) > 1')->get();
        $approved_id = SurveyShift::selectRaw('id')->where('approved', 1)->get();
        $lokasiIds = SurveyShift::selectRaw('lokasi_id')->where('approved', 1)->distinct()->get();

        $survey_shifts = SurveyShift::select('survey_shifts.*', 'users.name', 'lokasis.nama_lokasi', 'lokasis.jurusan_id')
            ->leftJoin('users', 'users.id', '=', 'survey_shifts.user_id')
            ->leftJoin('lokasis', 'lokasis.id', '=', 'survey_shifts.lokasi_id')
            ->when(($search), function ($query) use ($search) {
                $query->whereRaw('(users.name LIKE "%' . $search . '%" OR lokasis.nama_lokasi LIKE "%' . $search . '%")');
            })
            ->when($jurusan, function ($query) use ($jurusan) {
                $query->where('lokasis.jurusan_id', '=', $jurusan);
            })
            ->when($duplikat, function ($query) use ($duplicates) {
                $query->whereIn('survey_shifts.user_id', $duplicates);
            })
            ->when($approved, function ($query) use ($approved_id) {
                $query->whereIn('survey_shifts.id', $approved_id);
            })
            ->when($not_approved, function ($query) use ($lokasiIds) {
                $query->whereNotIn('survey_shifts.lokasi_id', $lokasiIds);
            })
            ->orderBy('survey_shifts.lokasi_id', 'ASC')
            ->orderBy('survey_shifts.user_id', 'ASC')
            ->paginate(20)
            ->withQueryString();

        return view('surveyshift.data', [
            'title' => 'Pemetaan Jadwal PKL',
            'data' => $survey_shifts,
            'data_jurusan' => Jurusan::orderBy('id', 'ASC')->get()
        ]);
    }

    public function cleanData()
    {
        // dd("test");
        $datasurvey = SurveyShift::orderBy('lokasi_id', 'ASC')->get();

        $jumlah = 0;
        foreach ($datasurvey as $ds) {
            $updated = $ds->jadwal;
            if (
                str_contains($updated, '"pulang":"01:')
                || str_contains($updated, '"pulang":"02:')
                || str_contains($updated, '"pulang":"03:')
                || str_contains($updated, '"pulang":"04:')
                || str_contains($updated, '"pulang":"05:')
                || str_contains($updated, '"pulang":"06:')
            ) {
                // echo $ds->id . ' | ' . $updated . '<hr>';

                $updated = str_replace('"pulang":"01:', '"pulang":"13:', $updated);
                $updated = str_replace('"pulang":"02:', '"pulang":"14:', $updated);
                $updated = str_replace('"pulang":"03:', '"pulang":"15:', $updated);
                $updated = str_replace('"pulang":"04:', '"pulang":"16:', $updated);
                $updated = str_replace('"pulang":"05:', '"pulang":"17:', $updated);
                $updated = str_replace('"pulang":"06:', '"pulang":"18:', $updated);
                // echo $updated . '<br><br>';
                SurveyShift::where('id', $ds->id)->update(['jadwal' => $updated]);
                $jumlah++;
            }
            // echo $updated . '<br><br>';

        }
        return redirect('/survey-shift/data')->with('success', 'Berhasil membersihkan ' . $jumlah . ' data pemetaan jadwal.');
    }

    public function edit($id)
    {
        return view('surveyshift.edit', [
            'title' => 'Edit Pemetaan Jadwal PKL',
            'lokasi' => Lokasi::select('id', 'nama_lokasi')->get(),
            'surveydata' => SurveyShift::find($id)
        ]);
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'user_id' => 'required',
            'lokasi_id' => 'required',
            'senin' => 'required',
            'senin_masuk' => 'required_if:senin,1',
            'senin_pulang' => 'required_if:senin,1',
            'selasa' => 'required',
            'selasa_masuk' => 'required_if:selasa,1',
            'selasa_pulang' => 'required_if:selasa,1',
            'rabu' => 'required',
            'rabu_masuk' => 'required_if:rabu,1',
            'rabu_pulang' => 'required_if:rabu,1',
            'kamis' => 'required',
            'kamis_masuk' => 'required_if:kamis,1',
            'kamis_pulang' => 'required_if:kamis,1',
            'jumat' => 'required',
            'jumat_masuk' => 'required_if:jumat,1',
            'jumat_pulang' => 'required_if:jumat,1',
            'sabtu' => 'required',
            'sabtu_masuk' => 'required_if:sabtu,1',
            'sabtu_pulang' => 'required_if:sabtu,1',
            'minggu' => 'required',
            'minggu_masuk' => 'required_if:minggu,1',
            'minggu_pulang' => 'required_if:minggu,1',
        ]);

        $jadwal = [
            'Mon' => ['jadwal' => $request->senin],
            'Tue' => ['jadwal' => $request->selasa],
            'Wed' => ['jadwal' => $request->rabu],
            'Thu' => ['jadwal' => $request->kamis],
            'Fri' => ['jadwal' => $request->jumat],
            'Sat' => ['jadwal' => $request->sabtu],
            'Sun' => ['jadwal' => $request->minggu],
        ];

        if ($request->senin == 1) {
            $jadwal['Mon']['masuk'] = $request->senin_masuk;
            $jadwal['Mon']['pulang'] = $request->senin_pulang;
        } else {
            $jadwal['Mon']['masuk'] = null;
            $jadwal['Mon']['pulang'] = null;
        }
        if ($request->selasa == 1) {
            $jadwal['Tue']['masuk'] = $request->selasa_masuk;
            $jadwal['Tue']['pulang'] = $request->selasa_pulang;
        } else {
            $jadwal['Tue']['masuk'] = null;
            $jadwal['Tue']['pulang'] = null;
        }
        if ($request->rabu == 1) {
            $jadwal['Wed']['masuk'] = $request->rabu_masuk;
            $jadwal['Wed']['pulang'] = $request->rabu_pulang;
        } else {
            $jadwal['Wed']['masuk'] = null;
            $jadwal['Wed']['pulang'] = null;
        }
        if ($request->kamis == 1) {
            $jadwal['Thu']['masuk'] = $request->kamis_masuk;
            $jadwal['Thu']['pulang'] = $request->kamis_pulang;
        } else {
            $jadwal['Thu']['masuk'] = null;
            $jadwal['Thu']['pulang'] = null;
        }
        if ($request->jumat == 1) {
            $jadwal['Fri']['masuk'] = $request->jumat_masuk;
            $jadwal['Fri']['pulang'] = $request->jumat_pulang;
        } else {
            $jadwal['Fri']['masuk'] = null;
            $jadwal['Fri']['pulang'] = null;
        }
        if ($request->sabtu == 1) {
            $jadwal['Sat']['masuk'] = $request->sabtu_masuk;
            $jadwal['Sat']['pulang'] = $request->sabtu_pulang;
        } else {
            $jadwal['Sat']['masuk'] = null;
            $jadwal['Sat']['pulang'] = null;
        }
        if ($request->minggu == 1) {
            $jadwal['Sun']['masuk'] = $request->minggu_masuk;
            $jadwal['Sun']['pulang'] = $request->minggu_pulang;
        } else {
            $jadwal['Sun']['masuk'] = null;
            $jadwal['Sun']['pulang'] = null;
        }
        $jadwal = json_encode($jadwal);

        SurveyShift::find($id)->update([
            'lokasi_id' => $request->lokasi_id,
            'jadwal' => $jadwal
        ]);

        $lokasi = SurveyShift::find($id)->Lokasi->nama_lokasi;
        return redirect('/survey-shift/data?search=' . $lokasi)->with('success', 'Data Pemetaan Berhasil Diupdate');
    }

    public function useThis($id)
    {
        $survey = SurveyShift::findOrFail($id);
        $lokasi_id = $survey->lokasi_id;
        $jadwal = $survey->jadwal;

        SurveyShift::where('lokasi_id', $lokasi_id)->update(['jadwal' => $jadwal]);
        return redirect('/survey-shift/data?search=' . $survey->Lokasi->nama_lokasi)->with('success', 'Data Pemetaan Berhasil Diupdate');
    }

    public function approve($id)
    {
        $jadwal = SurveyShift::findOrFail($id);
        return view('surveyshift.approve', [
            'title' => 'Persetujuan Jadwal PKL',
            'data' => $jadwal,
        ]);
    }

    public function simpanApprove(Request $request)
    {
        $request->validate([
            'nama_jadwal' => 'required',
            'id' => 'required',
        ]);

        $pilih_libur = $request['pilih_libur'] ?? 0;
        $jadwal_harian = $request['jadwal_harian'] ?? 0;

        $survey = SurveyShift::findOrFail($request->id);

        // $scek = [
        //     'nama_jadwal' => $request->nama_jadwal,
        //     'approved' => '1',
        //     'pilih_libur' => $pilih_libur,
        //     'jadwal_harian' => $jadwal_harian
        // ];

        $survey->update([
            'nama_jadwal' => $request->nama_jadwal,
            'approved' => '1',
            'pilih_libur' => $pilih_libur,
            'jadwal_harian' => $jadwal_harian
        ]);

        return redirect('/survey-shift/data')->with('success', 'Jadwal ' . $survey->Lokasi->nama_lokasi . ' telah disetujui');
    }

    public function reject($id)
    {
        $survey = SurveyShift::findOrFail($id);
        $survey->update([
            'nama_jadwal' => null,
            'approved' => '0'
        ]);

        return redirect('/survey-shift/data')->with('success', 'Jadwal <b>' . $survey->Lokasi->nama_lokasi . '</b> telah dibatalkan');
    }

    public function delete($id)
    {
        SurveyShift::findOrFail($id)->delete();
        return redirect('/survey-shift/data')->with('success', 'Data Pemetaan Berhasil Dihapus');
    }

    public function pilih()
    {
        $approved = SurveyShift::where('lokasi_id', auth()->user()->Lokasi->id)->where('approved', 1)->count();
        if (!$approved) {
            return redirect('/dashboard');
        }

        return view('surveyshift.pilih', [
            'title' => 'Pilih Jadwal PKL',
            'data' => SurveyShift::where('lokasi_id', auth()->user()->Lokasi->id)->where('approved', 1)->orderBy('nama_jadwal', 'ASC')->get()
        ]);
    }

    public function simpanJadwal(Request $request)
    {
        date_default_timezone_set('Asia/Jakarta');

        $rules = [
            'survey_shift_id' => 'required',
            'hari_libur' => 'required_if:pilih_libur,1',

        ];

        $messages = [
            'survey_shift_id.required' => 'Pilih jadwal terlebih dahulu!',
            'hari_libur.required_if' => 'Pilih hari libur terlebih dahulu!',
        ];

        $request->validate($rules, $messages);

        $user_id = auth()->user()->id;

        // ----------------------- khusus jadwal harian -----------------------
        $jadwal_harian = SurveyShift::select('id')->where('id', $request->survey_shift_id)->where('jadwal_harian', 1)->count();
        $lokasi_id = SurveyShift::where('id', $request->survey_shift_id)->first()->lokasi_id;

        if ($jadwal_harian) {
            $datex = new \DateTime();
            // $datex->modify('+1 day');

            // $datex->modify('sunday this week');

            $tanggal = $datex->format("Y-m-d");
            $jadwal = json_decode(SurveyShift::where('id', $request->survey_shift_id)->first()->jadwal, true);
            $harian = $jadwal[$datex->format('D') . ""];

            $cek = MappingShift::where('user_id', $user_id)
                ->where('tanggal', $tanggal)->first();

            if (!$cek) {
                $status_absen = '';
                if ($request->hari_libur == $datex->format('D') . "" || $harian["jadwal"] == 2) {
                    $status_absen = "Libur";
                } else {
                    $status_absen = "Tidak Masuk";
                }

                $validatedData = [
                    'user_id' => $user_id,
                    'tanggal' => $tanggal,
                    'jam_masuk' => ($status_absen == "Libur") ? "00:00" : $harian["masuk"],
                    'jam_keluar' => ($status_absen == "Libur") ? "00:00" : $harian["pulang"],
                    'status_absen' => $status_absen,
                ];

                // $validatedData['lock_location'] = $request['lock_location'] ? $request['lock_location'] : null;
                $validatedData['lock_location'] = Lokasi::where('id', $lokasi_id)->first()->lock_location;
                $validatedData['telat'] = 0;
                $validatedData['pulang_cepat'] = 0;
                // $validatedData['created_at'] = Carbon::now();
                // $validatedData['updated_at'] = Carbon::now();

                //MappingShift::create($validatedData);
                MappingShift::insert($validatedData);
            } else {
                //update jam masuk dan jam keluar jadwal yang sudah ada
                $updated = [
                    'jam_masuk' => $harian["masuk"],
                    'jam_keluar' => $harian["pulang"],
                ];
                MappingShift::where('user_id', $user_id)->where('id', $cek->id)
                    ->where('tanggal', $tanggal)->update($updated);
            }

            //insert hari berikutnya jika libur
            $datex->modify('+1 day');

            $tanggal = $datex->format("Y-m-d");

            $cek = MappingShift::where('user_id', $user_id)
                ->where('tanggal', $tanggal)->first();

            $harian = $jadwal[$datex->format('D') . ""];
            if (!$cek && $harian["jadwal"] == 2) {

                $status_absen = "Libur";

                $validatedData = [
                    'user_id' => $user_id,
                    'tanggal' => $tanggal,
                    'jam_masuk' => ($status_absen == "Libur") ? "00:00" : $harian["masuk"],
                    'jam_keluar' => ($status_absen == "Libur") ? "00:00" : $harian["pulang"],
                    'status_absen' => $status_absen,
                ];

                // $validatedData['lock_location'] = $request['lock_location'] ? $request['lock_location'] : null;
                $validatedData['lock_location'] = Lokasi::where('id', $lokasi_id)->first()->lock_location;
                $validatedData['telat'] = 0;
                $validatedData['pulang_cepat'] = 0;
                // $validatedData['created_at'] = Carbon::now();
                // $validatedData['updated_at'] = Carbon::now();

                //MappingShift::create($validatedData);
                MappingShift::insert($validatedData);
                // dd('berikutnya insert');
            } elseif ($harian["jadwal"] == 2) {

                //update jam masuk dan jam keluar jadwal yang sudah ada
                $status_absen = "Libur";
                $updated = [
                    'jam_masuk' => $harian["masuk"],
                    'jam_keluar' => $harian["pulang"],
                    'status_absen' => $status_absen
                ];
                MappingShift::where('user_id', $user_id)->where('tanggal', $tanggal)->update($updated);
                // dd('berikutnya update');
            }

            return redirect()->intended('/dashboard')->with('success', 'Jadwal PKL hari ini berhasil disimpan.');
        }
        // ----------------------- end khusus jadwal harian -----------------------

        $datex = new \DateTime();
        $datex->modify('monday this week');
        $tanggal_mulai =  $datex->format('Y-m-d');

        $datex->modify('sunday this week');
        $tanggal_akhir =  $datex->format('Y-m-d');

        $data_jadwal = [
            'user_id' => $user_id,
            'jadwal_id' => $request->survey_shift_id,
            'hari_libur' => $request->hari_libur,
            'tanggal_mulai' => $tanggal_mulai,
            'tanggal_akhir' => $tanggal_akhir,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ];

        $cek = PilihJadwal::where('user_id', $user_id)->where('tanggal_mulai', $tanggal_mulai)->where('tanggal_akhir', $tanggal_akhir)->count();
        if ($cek) {
            // return redirect()->intended('/dashboard')->with('error', 'Anda sudah memilih jadwal PKL minggu ini.');
            // Alert::error('Failed', 'Anda sudah memilih jadwal PKL minggu ini.');
            // return redirect('/dashboard');
        }

        if (!$cek) {
            PilihJadwal::insert($data_jadwal);
        }

        //belum entri ke mapping shift ------------------------------------------------------

        $begin = new \DateTime($tanggal_mulai);
        $end = new \DateTime($tanggal_akhir);
        $end = $end->modify('+1 day');

        $interval = new \DateInterval('P1D'); //referensi : https://en.wikipedia.org/wiki/ISO_8601#Durations
        $daterange = new \DatePeriod($begin, $interval, $end);

        $jadwal = json_decode(SurveyShift::where('id', $request->survey_shift_id)->first()->jadwal, true);

        $bulk_insert = [];
        foreach ($daterange as $date) {
            $tanggal = $date->format("Y-m-d");

            // $temp = DB::table('mapping_shifts')
            //     ->where('user_id', $user_id)
            //     ->whereRaw(DB::raw('(status_absen = "' . "Tidak Masuk" . '" OR status_absen = "' . "Libur" . '")'))
            //     ->whereNull('jam_absen')
            //     ->whereNull('jam_pulang')->get();
            // dd($temp);

            // DB::table('mapping_shifts')
            //     ->where('user_id', $user_id)
            //     ->whereRaw(DB::raw('(status_absen = "' . "Tidak Masuk" . '" OR status_absen = "' . "Libur" . '")'))
            //     ->whereNull('jam_absen')
            //     ->whereNull('jam_pulang')
            //     ->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])
            //     ->delete();

            $harian = $jadwal[$date->format('D') . ""];

            $cek = MappingShift::where('user_id', $user_id)
                ->where('tanggal', $tanggal)->first();

            if (!$cek) {
                $status_absen = '';
                if ($request->hari_libur == $date->format('D') . "" || $harian["jadwal"] == 2) {
                    $status_absen = "Libur";
                } else {
                    $status_absen = "Tidak Masuk";
                }

                $validatedData = [
                    'user_id' => $user_id,
                    'tanggal' => $tanggal,
                    'jam_masuk' => ($status_absen == "Libur") ? "00:00" : $harian["masuk"],
                    'jam_keluar' => ($status_absen == "Libur") ? "00:00" : $harian["pulang"],
                    'status_absen' => $status_absen,
                ];

                // $validatedData['lock_location'] = $request['lock_location'] ? $request['lock_location'] : null;
                $validatedData['lock_location'] = Lokasi::where('id', $lokasi_id)->first()->lock_location;
                $validatedData['telat'] = 0;
                $validatedData['pulang_cepat'] = 0;
                // $validatedData['created_at'] = Carbon::now();
                // $validatedData['updated_at'] = Carbon::now();

                //MappingShift::create($validatedData);
                $bulk_insert[] = $validatedData;
            } else {
                //update jam masuk dan jam keluar jadwal yang sudah ada
                $updated = [
                    'jam_masuk' => $harian["masuk"],
                    'jam_keluar' => $harian["pulang"],
                ];
                MappingShift::where('user_id', $user_id)->where('id', $cek->id)
                    ->where('tanggal', $tanggal)->update($updated);
            }
        }
        // dd('cek akhir');
        MappingShift::insert($bulk_insert);

        ///-----------------------------------------------------------

        return redirect()->intended('/dashboard')->with('success', 'Jadwal PKL minggu ini berhasil disimpan.');
    }

    public function cekJadwalTerhapus()
    {
        $begin = new \DateTime('2025-08-01');
        $end = new \DateTime('2025-08-03');
        $end = $end->modify('+1 day');

        $interval = new \DateInterval('P1D'); //referensi : https://en.wikipedia.org/wiki/ISO_8601#Durations
        $daterange = new \DatePeriod($begin, $interval, $end);

        $user = User::select('id', 'name')->where('is_admin', 'user')->get();

        foreach ($daterange as $date) {
            foreach ($user as $datauser) {
                $ada = MappingShift::where('user_id', $datauser->id)->where('tanggal', $date->format('Y-m-d'))->count();
                if (!$ada) {
                    echo $datauser->name . '---' . $date->format('Y-m-d') . '--' . $ada . '<br>';
                    $validatedData = [
                        'user_id' => $datauser->id,
                        'tanggal' => $date->format('Y-m-d'),
                        'jam_masuk' => "00:00",
                        'jam_keluar' => "00:00",
                        'status_absen' => "Libur",
                    ];

                    // $validatedData['lock_location'] = $request['lock_location'] ? $request['lock_location'] : null;
                    $validatedData['lock_location'] = 0;
                    $validatedData['telat'] = 0;
                    $validatedData['pulang_cepat'] = 0;
                    // $validatedData['created_at'] = Carbon::now();
                    // $validatedData['updated_at'] = Carbon::now();

                    MappingShift::create($validatedData);
                }
            }
        }

        // dd('test');
        // $jadwal = json_decode(SurveyShift::where('id', $request->survey_shift_id)->first()->jadwal, true);

        // $bulk_insert = [];
        // foreach ($daterange as $date) {
        //     $tanggal = $date->format("Y-m-d");

        //     // $temp = DB::table('mapping_shifts')
        //     //     ->where('user_id', $user_id)
        //     //     ->whereRaw(DB::raw('(status_absen = "' . "Tidak Masuk" . '" OR status_absen = "' . "Libur" . '")'))
        //     //     ->whereNull('jam_absen')
        //     //     ->whereNull('jam_pulang')->get();
        //     // dd($temp);

        //     DB::table('mapping_shifts')
        //         ->where('user_id', $user_id)
        //         ->whereRaw(DB::raw('(status_absen = "' . "Tidak Masuk" . '" OR status_absen = "' . "Libur" . '")'))
        //         ->whereNull('jam_absen')
        //         ->whereNull('jam_pulang')
        //         ->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])
        //         ->delete();

        //     $harian = $jadwal[$date->format('D') . ""];

        //     $cek = MappingShift::where('user_id', $user_id)
        //         ->where('tanggal', $tanggal)->first();

        //     if (!$cek) {
        //         $status_absen = '';
        //         if ($request->hari_libur == $date->format('D') . "" || $harian["jadwal"] == 2) {
        //             $status_absen = "Libur";
        //         } else {
        //             $status_absen = "Tidak Masuk";
        //         }

        //         $validatedData = [
        //             'user_id' => $user_id,
        //             'tanggal' => $tanggal,
        //             'jam_masuk' => ($status_absen == "Libur") ? "00:00" : $harian["masuk"],
        //             'jam_keluar' => ($status_absen == "Libur") ? "00:00" : $harian["pulang"],
        //             'status_absen' => $status_absen,
        //         ];

        //         // $validatedData['lock_location'] = $request['lock_location'] ? $request['lock_location'] : null;
        //         $validatedData['lock_location'] = 0;
        //         $validatedData['telat'] = 0;
        //         $validatedData['pulang_cepat'] = 0;
        //         // $validatedData['created_at'] = Carbon::now();
        //         // $validatedData['updated_at'] = Carbon::now();

        //         //MappingShift::create($validatedData);
        //         $bulk_insert[] = $validatedData;
        //     } else {
        //         //update jam masuk dan jam keluar jadwal yang sudah ada
        //         $updated = [
        //             'jam_masuk' => $harian["masuk"],
        //             'jam_keluar' => $harian["pulang"],
        //         ];
        //         MappingShift::where('user_id', $user_id)->where('id', $cek->id)
        //             ->where('tanggal', $tanggal)->update($updated);
        //     }
        // }
        // // dd('cek akhir');
        // MappingShift::insert($bulk_insert);
    }

    public function updateJadwalAll()
    {
        date_default_timezone_set('Asia/Jakarta');

        $pilih_jadwal = PilihJadwal::all();

        foreach ($pilih_jadwal as $pj) {
            $user_id = $pj->user_id;
            $tanggal_mulai = $pj->tanggal_mulai;
            $tanggal_akhir = $pj->tanggal_akhir;
            //belum entri ke mapping shift ------------------------------------------------------

            $begin = new \DateTime($tanggal_mulai);
            $end = new \DateTime($tanggal_akhir);
            $end = $end->modify('+1 day');

            $interval = new \DateInterval('P1D'); //referensi : https://en.wikipedia.org/wiki/ISO_8601#Durations
            $daterange = new \DatePeriod($begin, $interval, $end);

            $jadwal = json_decode(SurveyShift::where('id', $pj->jadwal_id)->first()->jadwal, true);
            $lokasi_id = SurveyShift::where('id', $pj->jadwal_id)->first()->lokasi_id;

            $bulk_insert = [];
            foreach ($daterange as $date) {
                $tanggal = $date->format("Y-m-d");

                // $temp = DB::table('mapping_shifts')
                //     ->where('user_id', $user_id)
                //     ->whereRaw(DB::raw('(status_absen = "' . "Tidak Masuk" . '" OR status_absen = "' . "Libur" . '")'))
                //     ->whereNull('jam_absen')
                //     ->whereNull('jam_pulang')->get();
                // dd($temp);

                DB::table('mapping_shifts')
                    ->where('user_id', $user_id)
                    ->whereRaw(DB::raw('(status_absen = "' . "Tidak Masuk" . '" OR status_absen = "' . "Libur" . '")'))
                    ->whereNull('jam_absen')
                    ->whereNull('jam_pulang')
                    ->delete();

                $harian = $jadwal[$date->format('D') . ""];

                $cek = MappingShift::where('user_id', $user_id)
                    ->where('tanggal', $tanggal)->first();

                if (!$cek) {
                    $status_absen = '';
                    if ($pj->hari_libur == $date->format('D') . "" || $harian["jadwal"] == 2) {
                        $status_absen = "Libur";
                    } else {
                        $status_absen = "Tidak Masuk";
                    }

                    $validatedData = [
                        'user_id' => $user_id,
                        'tanggal' => $tanggal,
                        'jam_masuk' => ($status_absen == "Libur") ? "00:00" : $harian["masuk"],
                        'jam_keluar' => ($status_absen == "Libur") ? "00:00" : $harian["pulang"],
                        'status_absen' => $status_absen,
                    ];

                    // $validatedData['lock_location'] = $request['lock_location'] ? $request['lock_location'] : null;
                    $validatedData['lock_location'] = Lokasi::where('id', $lokasi_id)->first()->lock_location;
                    $validatedData['telat'] = 0;
                    $validatedData['pulang_cepat'] = 0;
                    // $validatedData['created_at'] = Carbon::now();
                    // $validatedData['updated_at'] = Carbon::now();
                    echo "<br>insert --- ";
                    print_r($validatedData);
                    //MappingShift::create($validatedData);
                    $bulk_insert[] = $validatedData;
                } else {
                    //update jam masuk dan jam keluar jadwal yang sudah ada
                    $updated = [
                        'jam_masuk' => $harian["masuk"],
                        'jam_keluar' => $harian["pulang"],
                    ];

                    echo "<br>update --- ";
                    print_r($updated);
                    MappingShift::where('user_id', $user_id)->where('id', $cek->id)
                        ->where('tanggal', $tanggal)->update($updated);
                }
            }
            // dd('cek akhir');
            MappingShift::insert($bulk_insert);
        }


        ///-----------------------------------------------------------

        // return redirect()->intended('/dashboard')->with('success', 'Jadwal PKL minggu ini berhasil disimpan.');
    }

    public function updateJadwalOnlyTime()
    {
        date_default_timezone_set('Asia/Jakarta');


        //dd('test');
        // $pilih_jadwal = PilihJadwal::all();
        $pilih_jadwal = DB::select('select id, user_id, jadwal_id, hari_libur, tanggal_mulai, tanggal_akhir from pilih_jadwals');

        foreach ($pilih_jadwal as $pj) {
            $user_id = $pj->user_id;
            $tanggal_mulai = $pj->tanggal_mulai;
            $tanggal_akhir = $pj->tanggal_akhir;
            //belum entri ke mapping shift ------------------------------------------------------

            $begin = new \DateTime($tanggal_mulai);
            $end = new \DateTime($tanggal_akhir);
            $end = $end->modify('+1 day');

            $interval = new \DateInterval('P1D'); //referensi : https://en.wikipedia.org/wiki/ISO_8601#Durations
            $daterange = new \DatePeriod($begin, $interval, $end);

            $jadwal = json_decode(SurveyShift::where('id', $pj->jadwal_id)->first()->jadwal, true);

            //$bulk_insert = [];
            foreach ($daterange as $date) {
                $tanggal = $date->format("Y-m-d");

                $harian = $jadwal[$date->format('D') . ""];

                $cek = MappingShift::where('user_id', $user_id)
                    ->where('tanggal', $tanggal)->first();


                $updated = [
                    'jam_masuk' => $harian["masuk"],
                    'jam_keluar' => $harian["pulang"],
                ];

                //update jam masuk dan jam keluar jadwal yang sudah ada
                if ($pj->hari_libur == $date->format('D') . "" || $harian["jadwal"] == 2) {
                    $status_absen = 'Libur';
                    $updated = [
                        'jam_masuk' => ($status_absen == "Libur") ? "00:00" : $harian["masuk"],
                        'jam_keluar' => ($status_absen == "Libur") ? "00:00" : $harian["pulang"],
                        'status_absen' => $status_absen,
                    ];
                }
                echo '<br>';
                // dd($cek);
                echo $cek->id . '----> ' . $cek->jam_masuk . '---' . $cek->jam_keluar;
                echo " >>> update --- ";
                print_r($updated);
                MappingShift::where('user_id', $user_id)->where('id', $cek->id)
                    ->where('tanggal', $tanggal)->update($updated);
            }
        }
        // dd('cek akhir');
        //MappingShift::insert($bulk_insert);
    }


    //generate jadwal reguler ---- tambahkan form untuk penentuan tanggal mulai dan tanggal selesai.
    public function generateJadwalReguler(Request $request)
    {
        date_default_timezone_set('Asia/Jakarta');
        DB::connection('mysql')->disableQueryLog();
        set_time_limit(180);

        $request->validate([
            'mulai' => 'required',
            'sampai' => 'required',
        ]);

        $where_lokasi = '';
        $lokasi_id = $request->lokasi_id;
        if ($lokasi_id != '') {
            $where_lokasi = 'AND ss.lokasi_id = ' . $lokasi_id;
        }
        $survey = DB::select(DB::raw('SELECT ss.*, l.nama_lokasi from survey_shifts ss, lokasis l
            WHERE l.id = ss.lokasi_id
            AND ss.approved = 1
            and ss.lokasi_id NOT IN (SELECT lokasi_id from survey_shifts WHERE approved = 1 group BY lokasi_id HAVING COUNT(lokasi_id) > 1)
            AND ss.pilih_libur = 0
            AND ss.jadwal_harian = 0
            AND ss.nama_jadwal = "Reguler"
            ' . $where_lokasi . '
            order BY ss.lokasi_id'));


        $survey_harian = DB::select(DB::raw('SELECT ss.*, l.nama_lokasi from survey_shifts ss, lokasis l
            WHERE l.id = ss.lokasi_id
            AND ss.approved = 1
            AND ss.jadwal_harian = 1
            ' . $where_lokasi . '
            order BY ss.lokasi_id
            LIMIT 1'));

        if (count($survey_harian)) {
            echo 'ada jadwal harian';
            $jadwal_harian = [];
            foreach ($survey_harian as $dsurvey_harian) {
                // $mulai = new \DateTime('2025-08-11');
                $mulai = new \DateTime($request->mulai);
                $tanggal_mulai =  $mulai->format('Y-m-d');

                $selesai = new \DateTime($request->sampai);
                // $selesai = new \DateTime('2025-08-17');
                $tanggal_akhir =  $selesai->format('Y-m-d');

                $user = User::select('id')->where('lokasi_id', $dsurvey_harian->lokasi_id)->where('is_admin', 'user')->get();

                //echo '--------------' . count($user) . '-------------';
                echo $dsurvey_harian->nama_lokasi . '(user: ' . count($user) . ')' . '<br>';

                foreach ($user as $u) {
                    $user_id = $u->id;

                    $data_jadwal = [
                        'user_id' => $user_id,
                        'jadwal_id' => $dsurvey_harian->id,
                        // 'hari_libur' => $request->hari_libur,
                        'tanggal_mulai' => $tanggal_mulai,
                        'tanggal_akhir' => $tanggal_akhir,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ];

                    $cek = PilihJadwal::select('id')->where('user_id', $user_id)->where('tanggal_mulai', $tanggal_mulai)->where('tanggal_akhir', $tanggal_akhir)->count();
                    if ($cek) {
                        // return redirect()->intended('/dashboard')->with('error', 'Anda sudah memilih jadwal PKL minggu ini.');
                        // Alert::error('Failed', 'Anda sudah memilih jadwal PKL minggu ini.');
                        // return redirect('/dashboard');
                    }

                    if (!$cek) {
                        //PilihJadwal::insert($data_jadwal);
                        $jadwal_harian[] = $data_jadwal;
                    }
                }
            }
            // dd($jadwal_harian);
            PilihJadwal::insert($jadwal_harian);
        }


        // echo count($survey);
        // echo '<hr>';
        $total_dudi = 0;
        $total_siswa = 0;
        $total_jadwal = 0;

        $bulk_insert = [];
        $jadwal_insert = [];
        foreach ($survey as $dsurvey) {
            $total_dudi++;

            // $mulai = new \DateTime('2025-08-11');
            $mulai = new \DateTime($request->mulai);
            $tanggal_mulai =  $mulai->format('Y-m-d');

            $selesai = new \DateTime($request->sampai);
            // $selesai = new \DateTime('2025-08-17');
            $tanggal_akhir =  $selesai->format('Y-m-d');

            $user = User::select('id')->where('lokasi_id', $dsurvey->lokasi_id)->where('is_admin', 'user')->get();

            //echo '--------------' . count($user) . '-------------';
            echo $dsurvey->nama_lokasi . '(user: ' . count($user) . ')' . '<br>';

            foreach ($user as $u) {
                $total_siswa++;
                $user_id = $u->id;

                $data_jadwal = [
                    'user_id' => $user_id,
                    'jadwal_id' => $dsurvey->id,
                    // 'hari_libur' => $request->hari_libur,
                    'tanggal_mulai' => $tanggal_mulai,
                    'tanggal_akhir' => $tanggal_akhir,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ];

                $cek = PilihJadwal::select('id')->where('user_id', $user_id)->where('tanggal_mulai', $tanggal_mulai)->where('tanggal_akhir', $tanggal_akhir)->count();
                if ($cek) {
                    // return redirect()->intended('/dashboard')->with('error', 'Anda sudah memilih jadwal PKL minggu ini.');
                    // Alert::error('Failed', 'Anda sudah memilih jadwal PKL minggu ini.');
                    // return redirect('/dashboard');
                }

                if (!$cek) {
                    //PilihJadwal::insert($data_jadwal);
                    $jadwal_insert[] = $data_jadwal;
                }

                //belum entri ke mapping shift ------------------------------------------------------

                $begin = new \DateTime($tanggal_mulai);
                $end = new \DateTime($tanggal_akhir);
                $end = $end->modify('+1 day');

                $interval = new \DateInterval('P1D'); //referensi : https://en.wikipedia.org/wiki/ISO_8601#Durations
                $daterange = new \DatePeriod($begin, $interval, $end);

                $jadwal = json_decode(SurveyShift::select('jadwal')->where('id', $dsurvey->id)->first()->jadwal, true);

                foreach ($daterange as $date) {
                    $tanggal = $date->format("Y-m-d");
                    $total_jadwal++;
                    // $temp = DB::table('mapping_shifts')
                    //     ->where('user_id', $user_id)
                    //     ->whereRaw(DB::raw('(status_absen = "' . "Tidak Masuk" . '" OR status_absen = "' . "Libur" . '")'))
                    //     ->whereNull('jam_absen')
                    //     ->whereNull('jam_pulang')->get();
                    // dd($temp);

                    // DB::table('mapping_shifts')
                    //     ->where('user_id', $user_id)
                    //     ->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])
                    //     ->whereRaw(DB::raw('(status_absen = "' . "Tidak Masuk" . '" OR status_absen = "' . "Libur" . '")'))
                    //     ->whereNull('jam_absen')
                    //     ->whereNull('jam_pulang')
                    //     ->delete();

                    $harian = $jadwal[$date->format('D') . ""];

                    $cek = MappingShift::select('id', 'status_absen')->where('user_id', $user_id)
                        ->where('tanggal', $tanggal)->first();

                    if (!$cek) {
                        $status_absen = '';
                        if ($harian["jadwal"] == 2) {
                            $status_absen = "Libur";
                        } else {
                            $status_absen = "Tidak Masuk";
                        }

                        $validatedData = [
                            'user_id' => $user_id,
                            'tanggal' => $tanggal,
                            'jam_masuk' => ($status_absen == "Libur") ? "00:00" : $harian["masuk"],
                            'jam_keluar' => ($status_absen == "Libur") ? "00:00" : $harian["pulang"],
                            'status_absen' => $status_absen,
                        ];

                        // $validatedData['lock_location'] = $request['lock_location'] ? $request['lock_location'] : null;
                        $validatedData['lock_location'] = Lokasi::where('id', $dsurvey->lokasi_id)->first()->lock_location;
                        $validatedData['telat'] = 0;
                        $validatedData['pulang_cepat'] = 0;
                        // $validatedData['created_at'] = Carbon::now();
                        // $validatedData['updated_at'] = Carbon::now();

                        //MappingShift::create($validatedData);
                        $bulk_insert[] = $validatedData;
                    } else {
                        echo 'ada jadwal tanggal' . $tanggal . '----> ' . $cek->id . '---' . $cek->jam_masuk . '---' . $cek->jam_keluar . '<br>';
                        //update jam masuk dan jam keluar jadwal yang sudah ada
                        $status_absen = '';
                        if ($harian["jadwal"] == 2) {
                            $status_absen = "Libur";
                        } else {
                            $status_absen = $cek->status_absen;
                        }

                        $updated = [
                            'jam_masuk' => ($status_absen == "Libur") ? "00:00" : $harian["masuk"],
                            'jam_keluar' => ($status_absen == "Libur") ? "00:00" : $harian["pulang"],
                            'status_absen' => $status_absen,
                        ];

                        // MappingShift::where('user_id', $user_id)->where('id', $cek->id)->whereNull('jam_absen')->orWhereNull('jam_pulang')
                        //     ->where('tanggal', $tanggal)->update($updated);
                        MappingShift::where('user_id', $user_id)->where('id', $cek->id)->where('tanggal', $tanggal)->update($updated);

                        echo '-----------update : ';
                        print_r($updated);
                        echo '<hr>';
                    }
                }
            }
            // dd('cek akhir');

            ///-----------------------------------------------------------

            //return redirect()->intended('/dashboard')->with('success', 'Jadwal PKL minggu ini berhasil disimpan.');
        };
        PilihJadwal::insert($jadwal_insert);
        MappingShift::insert($bulk_insert);
        echo '<hr>';
        echo 'total dudi : ' . $total_dudi;
        echo '<br>';
        echo 'total siswa : ' . $total_siswa;
        echo '<br>';
        echo 'total jadwal : ' . $total_jadwal;
    }


    //generate jadwal reguler ---- tambahkan form untuk penentuan tanggal mulai dan tanggal selesai.
    public function generateLiburReguler(Request $request)
    {
        date_default_timezone_set('Asia/Jakarta');
        DB::connection('mysql')->disableQueryLog();
        set_time_limit(180);

        $request->validate([
            'mulai' => 'required',
            'sampai' => 'required',
        ]);

        $where_lokasi = '';
        $lokasi_id = $request->lokasi_id;
        if ($lokasi_id != '') {
            $where_lokasi = 'AND ss.lokasi_id = ' . $lokasi_id;
        }
        $survey = DB::select(DB::raw('SELECT ss.*, l.nama_lokasi from survey_shifts ss, lokasis l
            WHERE l.id = ss.lokasi_id
            AND ss.approved = 1
            and ss.lokasi_id NOT IN (SELECT lokasi_id from survey_shifts WHERE approved = 1 group BY lokasi_id HAVING COUNT(lokasi_id) > 1)
            AND ss.pilih_libur = 0
            AND ss.jadwal_harian = 0
            AND ss.nama_jadwal = "Reguler"
            ' . $where_lokasi . '
            order BY ss.lokasi_id'));

        // echo count($survey);
        // echo '<hr>';
        $total_dudi = 0;
        $total_siswa = 0;
        $total_jadwal = 0;

        $bulk_insert = [];
        foreach ($survey as $dsurvey) {
            $total_dudi++;

            // $mulai = new \DateTime('2025-08-11');
            $mulai = new \DateTime($request->mulai);
            $tanggal_mulai =  $mulai->format('Y-m-d');

            $selesai = new \DateTime($request->sampai);
            // $selesai = new \DateTime('2025-08-17');
            $tanggal_akhir =  $selesai->format('Y-m-d');

            $user = User::select('id')->where('lokasi_id', $dsurvey->lokasi_id)->where('is_admin', 'user')->get();

            $libur = MappingShift::select('id')->whereRaw('user_id IN (SELECT id FROM users WHERE lokasi_id = ' . $dsurvey->lokasi_id . ')')->whereRaw('status_absen = "Libur"')->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])->get();

            // print_r($user);
            // echo '<hr>';
            //echo '--------------' . count($user) . '-------------';
            // echo $dsurvey->nama_lokasi . '(user: ' . count($user) . ')' . '(libur: '.count($libur).')<br>';
            if (count($user) != count($libur) && count($libur) > 0) {
                echo $dsurvey->nama_lokasi . '(user: ' . count($user) . ')' . '(libur: ' . count($libur) . ')<br>';
            }

            // foreach ($user as $u) {
            //     $total_siswa++;
            //     $user_id = $u->id;

            //     //belum entri ke mapping shift ------------------------------------------------------

            //     $begin = new \DateTime($tanggal_mulai);
            //     $end = new \DateTime($tanggal_akhir);
            //     $end = $end->modify('+1 day');

            //     $interval = new \DateInterval('P1D'); //referensi : https://en.wikipedia.org/wiki/ISO_8601#Durations
            //     $daterange = new \DatePeriod($begin, $interval, $end);

            //     $jadwal = json_decode(SurveyShift::select('jadwal')->where('id', $dsurvey->id)->first()->jadwal, true);

            //     foreach ($daterange as $date) {
            //         $tanggal = $date->format("Y-m-d");
            //         $total_jadwal++;
            //         // $temp = DB::table('mapping_shifts')
            //         //     ->where('user_id', $user_id)
            //         //     ->whereRaw(DB::raw('(status_absen = "' . "Tidak Masuk" . '" OR status_absen = "' . "Libur" . '")'))
            //         //     ->whereNull('jam_absen')
            //         //     ->whereNull('jam_pulang')->get();
            //         // dd($temp);

            //         // DB::table('mapping_shifts')
            //         //     ->where('user_id', $user_id)
            //         //     ->whereBetween('tanggal', [$tanggal_mulai, $tanggal_akhir])
            //         //     ->whereRaw(DB::raw('(status_absen = "' . "Tidak Masuk" . '" OR status_absen = "' . "Libur" . '")'))
            //         //     ->whereNull('jam_absen')
            //         //     ->whereNull('jam_pulang')
            //         //     ->delete();

            //         $harian = $jadwal[$date->format('D') . ""];

            //         $cek = MappingShift::select('id')->where('user_id', $user_id)
            //             ->where('tanggal', $tanggal)->first();

            //         if (!$cek) {
            //             $status_absen = '';
            //             if ($harian["jadwal"] == 2) {
            //                 $status_absen = "Libur";
            //             } else {
            //                 $status_absen = "Tidak Masuk";
            //             }

            //             $validatedData = [
            //                 'user_id' => $user_id,
            //                 'tanggal' => $tanggal,
            //                 'jam_masuk' => ($status_absen == "Libur") ? "00:00" : $harian["masuk"],
            //                 'jam_keluar' => ($status_absen == "Libur") ? "00:00" : $harian["pulang"],
            //                 'status_absen' => $status_absen,
            //             ];

            //             // $validatedData['lock_location'] = $request['lock_location'] ? $request['lock_location'] : null;
            //             $validatedData['lock_location'] = Lokasi::where('id', $dsurvey->lokasi_id)->first()->lock_location;
            //             $validatedData['telat'] = 0;
            //             $validatedData['pulang_cepat'] = 0;
            //             // $validatedData['created_at'] = Carbon::now();
            //             // $validatedData['updated_at'] = Carbon::now();

            //             //MappingShift::create($validatedData);
            //             $bulk_insert[] = $validatedData;
            //         } else {
            //             //update jam masuk dan jam keluar jadwal yang sudah ada
            //             $status_absen = '';
            //             if ($harian["jadwal"] == 2) {
            //                 $status_absen = "Libur";
            //             } else {
            //                 $status_absen = "Tidak Masuk";
            //             }

            //             $updated = [
            //                 'jam_masuk' => ($status_absen == "Libur") ? "00:00" : $harian["masuk"],
            //                 'jam_keluar' => ($status_absen == "Libur") ? "00:00" : $harian["pulang"],
            //                 'status_absen' => $status_absen,
            //             ];

            //             MappingShift::where('user_id', $user_id)->where('id', $cek->id)->whereNull('jam_absen')->orWhereNull('jam_pulang')
            //                 ->where('tanggal', $tanggal)->update($updated);

            //             echo '-----------update : ';
            //             print_r($updated);
            //             echo '<hr>';
            //         }
            //     }
            // }
            // dd('cek akhir');

            ///-----------------------------------------------------------

            //return redirect()->intended('/dashboard')->with('success', 'Jadwal PKL minggu ini berhasil disimpan.');
        };
        // MappingShift::insert($bulk_insert);
        echo '<hr>';
        echo 'total dudi : ' . $total_dudi;
        echo '<br>';
        echo 'total siswa : ' . $total_siswa;
        echo '<br>';
        echo 'total jadwal : ' . $total_jadwal;
    }

    public function jadwal()
    {
        // dd('test');
        $search = request()->input('search');
        $lokasi_id = request()->input('lokasi_id');

        $jadwal = PilihJadwal::select('pilih_jadwals.*', 'users.name', 'lokasis.nama_lokasi')
            ->leftJoin('users', 'pilih_jadwals.user_id', '=', 'users.id')
            ->leftJoin('lokasis', 'users.lokasi_id', '=', 'lokasis.id')
            ->when($search, function ($query) use ($search) {
                // $query->whereHas('User', function ($query) use ($search) {
                $query->where('users.name', 'LIKE', '%' . $search . '%');
                $query->orWhere('lokasis.nama_lokasi', 'LIKE', '%' . $search . '%');
                // });
            })
            ->when($lokasi_id, function ($query) use ($lokasi_id) {
                $query->where('users.lokasi_id', $lokasi_id);
            })
            ->orderBy('tanggal_mulai', 'DESC')
            ->orderBy('nama_lokasi', 'ASC')
            ->orderBy('name', 'ASC')
            // ->orderBy('jadwal_id')
            ->paginate(20)->withQueryString();


        return view('surveyshift.jadwal', [
            'title' => 'Jadwal PKL Siswa',
            'jadwal' => $jadwal,
            'lokasi' => Lokasi::select('id', 'nama_lokasi')->get(),
        ]);
    }

    public function detailJadwal($id)
    {
        date_default_timezone_set('Asia/Jakarta');

        $pilih_jadwal = PilihJadwal::findOrFail($id);

        $mulai = new \DateTime($pilih_jadwal->tanggal_mulai);
        $akhir = new \DateTime($pilih_jadwal->tanggal_akhir);

        $tglawal =  $mulai->format('Y-m-d');
        $tglakhir =  $akhir->format('Y-m-d');

        $jadwal = MappingShift::select('id', 'tanggal', 'jam_masuk', 'jam_keluar', 'status_absen')
            ->where('user_id', $pilih_jadwal->user_id)
            ->whereBetween('tanggal', [$tglawal, $tglakhir])
            ->orderBy('tanggal', 'ASC')
            ->get();

        $user = User::find($pilih_jadwal->user_id);

        return view('surveyshift.editAdmin', [
            'title' => 'Edit Jadwal PKL',
            'jadwal' => $jadwal,
            'user' => $user,
        ]);
    }
    public function updateJadwal(Request $request, $id)
    {
        date_default_timezone_set('Asia/Jakarta');

        $request->validate([
            'user_id' => 'required',
            'tanggal_mulai' => 'required',
            'tanggal_akhir' => 'required',
            'senin' => 'required',
            'senin_masuk' => 'required_if:senin,1',
            'senin_pulang' => 'required_if:senin,1',
            'selasa' => 'required',
            'selasa_masuk' => 'required_if:selasa,1',
            'selasa_pulang' => 'required_if:selasa,1',
            'rabu' => 'required',
            'rabu_masuk' => 'required_if:rabu,1',
            'rabu_pulang' => 'required_if:rabu,1',
            'kamis' => 'required',
            'kamis_masuk' => 'required_if:kamis,1',
            'kamis_pulang' => 'required_if:kamis,1',
            'jumat' => 'required',
            'jumat_masuk' => 'required_if:jumat,1',
            'jumat_pulang' => 'required_if:jumat,1',
            'sabtu' => 'required',
            'sabtu_masuk' => 'required_if:sabtu,1',
            'sabtu_pulang' => 'required_if:sabtu,1',
            'minggu' => 'required',
            'minggu_masuk' => 'required_if:minggu,1',
            'minggu_pulang' => 'required_if:minggu,1',
        ]);

        $jadwal = [
            'Mon' => ['jadwal' => $request->senin],
            'Tue' => ['jadwal' => $request->selasa],
            'Wed' => ['jadwal' => $request->rabu],
            'Thu' => ['jadwal' => $request->kamis],
            'Fri' => ['jadwal' => $request->jumat],
            'Sat' => ['jadwal' => $request->sabtu],
            'Sun' => ['jadwal' => $request->minggu],
        ];

        if ($request->senin == 1) {
            $jadwal['Mon']['masuk'] = $request->senin_masuk;
            $jadwal['Mon']['pulang'] = $request->senin_pulang;
        } else {
            $jadwal['Mon']['masuk'] = null;
            $jadwal['Mon']['pulang'] = null;
        }
        if ($request->selasa == 1) {
            $jadwal['Tue']['masuk'] = $request->selasa_masuk;
            $jadwal['Tue']['pulang'] = $request->selasa_pulang;
        } else {
            $jadwal['Tue']['masuk'] = null;
            $jadwal['Tue']['pulang'] = null;
        }
        if ($request->rabu == 1) {
            $jadwal['Wed']['masuk'] = $request->rabu_masuk;
            $jadwal['Wed']['pulang'] = $request->rabu_pulang;
        } else {
            $jadwal['Wed']['masuk'] = null;
            $jadwal['Wed']['pulang'] = null;
        }
        if ($request->kamis == 1) {
            $jadwal['Thu']['masuk'] = $request->kamis_masuk;
            $jadwal['Thu']['pulang'] = $request->kamis_pulang;
        } else {
            $jadwal['Thu']['masuk'] = null;
            $jadwal['Thu']['pulang'] = null;
        }
        if ($request->jumat == 1) {
            $jadwal['Fri']['masuk'] = $request->jumat_masuk;
            $jadwal['Fri']['pulang'] = $request->jumat_pulang;
        } else {
            $jadwal['Fri']['masuk'] = null;
            $jadwal['Fri']['pulang'] = null;
        }
        if ($request->sabtu == 1) {
            $jadwal['Sat']['masuk'] = $request->sabtu_masuk;
            $jadwal['Sat']['pulang'] = $request->sabtu_pulang;
        } else {
            $jadwal['Sat']['masuk'] = null;
            $jadwal['Sat']['pulang'] = null;
        }
        if ($request->minggu == 1) {
            $jadwal['Sun']['masuk'] = $request->minggu_masuk;
            $jadwal['Sun']['pulang'] = $request->minggu_pulang;
        } else {
            $jadwal['Sun']['masuk'] = null;
            $jadwal['Sun']['pulang'] = null;
        }

        $begin = new \DateTime($request->tanggal_mulai);
        $end = new \DateTime($request->tanggal_akhir);
        $end = $end->modify('+1 day');

        $interval = new \DateInterval('P1D'); //referensi : https://en.wikipedia.org/wiki/ISO_8601#Durations
        $daterange = new \DatePeriod($begin, $interval, $end);

        $bulk_insert = [];
        foreach ($daterange as $date) {
            $tanggal = $date->format("Y-m-d");
            $cek = MappingShift::where('user_id', $request->user_id)
                ->where('tanggal', $tanggal)->first();

            $harian = $jadwal[$date->format('D') . ""];
            if (!$cek) {
                $status_absen = '';
                if ($harian["jadwal"] == 2) {
                    $status_absen = "Libur";
                } else {
                    $status_absen = "Tidak Masuk";
                }
                $datajadwal = [
                    'user_id' => $request->user_id,
                    'tanggal' => $tanggal,
                    'jam_masuk' => ($status_absen == "Libur") ? "00:00" : $harian["masuk"],
                    'jam_keluar' => ($status_absen == "Libur") ? "00:00" : $harian["pulang"],
                    'status_absen' => $status_absen,
                ];
                $bulk_insert[] = $datajadwal;
            } else {
                $status_absen = '';
                if ($harian['jadwal'] == 2) {
                    $status_absen = 'Libur';
                } else {
                    $status_absen = 'Tidak Masuk';
                }
                if ($cek->status_absen != 'Libur' && $cek->status_absen != 'Tidak Masuk') {
                    $status_absen = $cek->status_absen;
                }

                if ($request->timpa_jadwal == 1 && $harian['jadwal'] == 2) {
                    $status_absen = 'Libur';
                }

                $datajadwal = [
                    'jam_masuk' => ($status_absen == 'Libur') ? "00:00" : $harian['masuk'],
                    'jam_keluar' => ($status_absen == 'Libur') ? "00:00" : $harian['pulang'],
                    'status_absen' => $status_absen,
                ];

                MappingShift::where('user_id', $request->user_id)
                    ->where('tanggal', $tanggal)->update($datajadwal);
            }
        }
        MappingShift::insert($bulk_insert);

        AbsenController::recalculateTime(null, $request->user_id, $request->tanggal_mulai, $request->tanggal_akhir);
        return redirect('/jadwal')->with('success', 'Jadwal Mingguan dari ' . User::find($request->user_id)->name . ' berhasil diupdate');
    }

    public function deleteJadwal($id)
    {
        $previous_url = request('previous_url');
        PilihJadwal::findOrFail($id)->delete();
        return redirect($previous_url)->with('success', 'Jadwal Berhasil Dihapus');
    }
}
