<?php

namespace App\Http\Livewire\Attendance;

use App\Models\MappingShift;
use Livewire\Component;

class UserAttendance extends Component
{
    public $shift_karyawan;
    public $currentTime;
    public $canCheckIn = false;
    public $canCheckOut = false;

    public function mount()
    {
        $this->loadAttendanceData();
        $this->updateCurrentTime();
    }

    public function loadAttendanceData()
    {
        date_default_timezone_set('Asia/Jakarta');
        $user_login = auth()->user()->id;
        $tanggal = date('Y-m-d');

        $this->shift_karyawan = MappingShift::select('id', 'tanggal', 'jam_masuk', 'jam_keluar', 'lock_location', 'status_absen', 'jam_absen', 'jam_pulang')
            ->where('user_id', $user_login)
            ->where('tanggal', $tanggal)
            ->first();

        // Determine if user can check in or out
        if ($this->shift_karyawan) {
            $this->canCheckIn = is_null($this->shift_karyawan->jam_absen) &&
                $this->shift_karyawan->status_absen !== 'Libur' &&
                $this->shift_karyawan->status_absen !== 'Izin Tidak Masuk' &&
                $this->shift_karyawan->status_absen !== 'Izin Sakit';

            $this->canCheckOut = !is_null($this->shift_karyawan->jam_absen) &&
                is_null($this->shift_karyawan->jam_pulang) &&
                $this->shift_karyawan->status_absen === 'Masuk';
        }
    }

    public function updateCurrentTime()
    {
        $this->currentTime = date('H:i:s');
    }

    public function refreshData()
    {
        $this->loadAttendanceData();
        $this->updateCurrentTime();
        $this->emit('dataRefreshed');
    }

    public function render()
    {
        return view('livewire.attendance.user-attendance');
    }
}
