<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Shift extends Model
{
    use HasFactory;
    protected $guarded = ['id'];
    public $timestamps = false;

    public function MappingShift()
    {
        return $this->hasMany(MappingShift::class);
    }

    public function AutoShift()
    {
        return $this->hasMany(AutoShift::class);
    }

    public function dinasLuar()
    {
        return $this->hasMany(dinasLuar::class);
    }

    public function CreatedBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
