<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Shift;
use App\Models\Lokasi;
use App\Models\Jabatan;
use App\Models\AutoShift;
use App\Models\MappingShift;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AutoShiftController extends Controller
{
    public function index()
    {
        // $temp = AutoShift::all();
        // foreach ($temp as $shf) {
        //     $data['nama_shift'] = Lokasi::where('id', $shf->lokasi_id)->first()->nama_lokasi;
        //     Shift::find($shf->shift_id)->update($data);
        // }

        $search = request()->input('search');
        $auto_shift = AutoShift::when($search, function ($query) use ($search) {
            $query->orWhereHas('Shift', function ($query) use ($search) {
                $query->where('nama_shift', 'LIKE', '%' . $search . '%');
            })
                ->orWhereHas('Lokasi', function ($query) use ($search) {
                    $query->where('nama_lokasi', 'LIKE', '%' . $search . '%');
                });
        })
            ->orderBy('shift_id', 'ASC')
            ->paginate(20)
            ->withQueryString();

        return view('autoshift.index', [
            'title' => 'Master Jadwal Otomatis',
            'data' => $auto_shift,
        ]);
    }

    public function tambah()
    {
        return view('autoshift.tambah', [
            'title' => 'Tambah Data Jadwal Otomatis',
            'data_lokasi' => Lokasi::all(),
            'shift' => Shift::all()
        ]);
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'lokasi_id' => 'required',
            'shift_id' => 'required',
            'tanggal_mulai' => 'required',
            'tanggal_akhir' => 'required',
        ]);

        $user = User::where('lokasi_id', $request->lokasi_id)
            ->where('is_admin', 'user')
            ->select(['id', 'name'])
            ->get();

        AutoShift::create($validatedData);

        if ($request["tanggal_mulai"] == null) {
            $request["tanggal_mulai"] = $request["tanggal_akhir"];
        } else {
            $request["tanggal_mulai"] = $request["tanggal_mulai"];
        }

        if ($request["tanggal_akhir"] == null) {
            $request["tanggal_akhir"] = $request["tanggal_mulai"];
        } else {
            $request["tanggal_akhir"] = $request["tanggal_akhir"];
        }

        $begin = new \DateTime($request["tanggal_mulai"]);
        $end = new \DateTime($request["tanggal_akhir"]);
        $end = $end->modify('+1 day');

        $interval = new \DateInterval('P1D'); //referensi : https://en.wikipedia.org/wiki/ISO_8601#Durations
        $daterange = new \DatePeriod($begin, $interval, $end);

        $bulk_insert = [];
        foreach ($daterange as $date) {
            $tanggal = $date->format("Y-m-d");

            foreach ($user as $datauser) {
                MappingShift::where('user_id', $datauser->id)
                    ->where('status_absen', 'Tidak Masuk')
                    ->orWhere('status_absen', 'Libur')
                    ->whereNull('jam_absen')
                    ->whereNull('jam_pulang')
                    ->delete();

                $cek = MappingShift::where('user_id', $datauser->id)
                    ->where('tanggal', $tanggal)->first();

                if (!$cek) {
                    if ($request["shift_id"] == 1) {
                        $request["status_absen"] = "Libur";
                    } else {
                        $request["status_absen"] = "Tidak Masuk";
                    }

                    $request["tanggal"] = $tanggal;

                    $validatedData = [
                        'user_id' => $datauser->id,
                        'shift_id' => $request["shift_id"],
                        'tanggal' => $tanggal,
                        'status_absen' => $request["status_absen"],
                    ];

                    $validatedData['lock_location'] = $request['lock_location'] ? $request['lock_location'] : null;
                    $validatedData['telat'] = 0;
                    $validatedData['pulang_cepat'] = 0;
                    $validatedData['created_at'] = Carbon::now();
                    $validatedData['updated_at'] = Carbon::now();

                    //MappingShift::create($validatedData);
                    $bulk_insert[] = $validatedData;
                }
            }
        }
        MappingShift::insert($bulk_insert);
        return redirect('/auto-shift')->with('success', 'Data Berhasil Ditambahkan');
    }

    public function edit($id)
    {
        return view('autoshift.edit', [
            'title' => 'Edit Data Jadwal Otomatis',
            'auto_shift' => AutoShift::findOrFail($id),
            'data_lokasi' => Lokasi::all(),
            // 'data_jabatan' => Jabatan::all(),
            'shift' => Shift::all()
        ]);
    }

    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'lokasi_id' => 'required',
            'shift_id' => 'required',
            'tanggal_mulai' => 'required',
            'tanggal_akhir' => 'required',
        ]);


        AutoShift::where('id', $id)->update($validatedData);

        if ($request["tanggal_mulai"] == null) {
            $request["tanggal_mulai"] = $request["tanggal_akhir"];
        } else {
            $request["tanggal_mulai"] = $request["tanggal_mulai"];
        }

        if ($request["tanggal_akhir"] == null) {
            $request["tanggal_akhir"] = $request["tanggal_mulai"];
        } else {
            $request["tanggal_akhir"] = $request["tanggal_akhir"];
        }

        $begin = new \DateTime($request["tanggal_mulai"]);
        $end = new \DateTime($request["tanggal_akhir"]);
        $end = $end->modify('+1 day');

        $interval = new \DateInterval('P1D'); //referensi : https://en.wikipedia.org/wiki/ISO_8601#Durations
        $daterange = new \DatePeriod($begin, $interval, $end);

        $user = User::where('lokasi_id', $request->lokasi_id)
            ->where('is_admin', 'user')
            ->select(['id', 'name'])
            ->get();

        $bulk_insert = [];
        foreach ($daterange as $date) {
            $tanggal = $date->format("Y-m-d");

            foreach ($user as $datauser) {
                MappingShift::where('user_id', $datauser->id)
                    ->where('status_absen', 'Tidak Masuk')
                    ->orWhere('status_absen', 'Libur')
                    ->whereNull('jam_absen')
                    ->whereNull('jam_pulang')
                    ->delete();

                $cek = MappingShift::where('user_id', $datauser->id)
                    ->where('tanggal', $tanggal)->first();

                $validatedData = [];
                if (!$cek) {
                    if ($request["shift_id"] == 1) {
                        $request["status_absen"] = "Libur";
                    } else {
                        $request["status_absen"] = "Tidak Masuk";
                    }

                    $request["tanggal"] = $tanggal;

                    $validatedData = [
                        'user_id' => $datauser->id,
                        'shift_id' => $request["shift_id"],
                        'tanggal' => $tanggal,
                        'status_absen' => $request["status_absen"],
                    ];

                    // $validatedData['lock_location'] = $request['lock_location'] ? $request['lock_location'] : null;
                    $validatedData['lock_location'] = 1;
                    $validatedData['telat'] = 0;
                    $validatedData['pulang_cepat'] = 0;
                    $validatedData['created_at'] = Carbon::now();
                    $validatedData['updated_at'] = Carbon::now();

                    $bulk_insert[] = $validatedData;
                    //MappingShift::create($validatedData);
                }
            }
        }
        MappingShift::insert($bulk_insert);
        return redirect('/auto-shift')->with('success', 'Data Berhasil Diupdate');
    }

    public function delete($id)
    {
        $delete = AutoShift::find($id);
        $delete->delete();
        return redirect('/auto-shift')->with('success', 'Data Berhasil di Delete');
    }


    public function generate()
    {
        $dms = MappingShift::where('status_absen', '!=', 'Tidak Masuk')
            ->orWhere('status_absen', '!=', 'Libur')
            ->whereNotNull('jam_absen')
            ->whereNotNull('jam_pulang')
            ->get();

        MappingShift::truncate();

        $bulk = [];
        foreach ($dms as $adms) {
            $xdms = $adms->toArray();
            $xdms['created_at'] = $xdms['created_at'] != null ? date('Y-m-d H:i:s', strtotime($xdms['created_at'])) : null;
            $xdms['updated_at'] = $xdms['updated_at'] != null ? date('Y-m-d H:i:s', strtotime($xdms['updated_at'])) : null;

            unset($xdms['id']);

            $bulk[] = $xdms;
        }
        MappingShift::insert($bulk);


        $a_shift = AutoShift::select('lokasi_id', 'shift_id', 'tanggal_mulai', 'tanggal_akhir')->orderBy('lokasi_id', 'ASC')->get();

        $bulk_insert = [];
        foreach ($a_shift as $da) {
            $begin = new \DateTime($da->tanggal_mulai);
            $end = new \DateTime($da->tanggal_akhir);
            $end = $end->modify('+1 day');

            $interval = new \DateInterval('P1D'); //referensi : https://en.wikipedia.org/wiki/ISO_8601#Durations
            $daterange = new \DatePeriod($begin, $interval, $end);

            $user = User::where('lokasi_id', $da->lokasi_id)
                ->where('is_admin', 'user')
                ->select(['id', 'name'])
                ->get();

            foreach ($daterange as $date) {
                $tanggal = $date->format("Y-m-d");

                foreach ($user as $datauser) {
                    MappingShift::where('user_id', $datauser->id)
                        ->where('status_absen', 'Tidak Masuk')
                        ->orWhere('status_absen', 'Libur')
                        ->whereNull('jam_absen')
                        ->whereNull('jam_pulang')
                        ->delete();

                    $cek = MappingShift::where('user_id', $datauser->id)
                        ->where('tanggal', $tanggal)->first();

                    $validatedData = [];
                    if (!$cek) {
                        $status_absen = "";
                        if ($da->shift_id == 1) {
                            $status_absen = "Libur";
                        } else {
                            $status_absen = "Tidak Masuk";
                        }

                        $validatedData = [
                            'user_id' => $datauser->id,
                            'shift_id' => $da->shift_id,
                            'tanggal' => $tanggal,
                            'status_absen' => $status_absen,
                        ];

                        $validatedData['lock_location'] = 0;
                        $validatedData['telat'] = 0;
                        $validatedData['pulang_cepat'] = 0;
                        $validatedData['created_at'] = Carbon::now();
                        $validatedData['updated_at'] = Carbon::now();

                        $bulk_insert[] = $validatedData;
                    }
                }
            }
        }

        MappingShift::insert($bulk_insert);
        return redirect('/auto-shift')->with('success', 'Data Berhasil Dibuat');
    }
}
