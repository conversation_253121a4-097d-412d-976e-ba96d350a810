<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- Mobile Specific Metas -->
    <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, viewport-fit=cover">
    <title>{{ $title }}</title>
    <!-- Favicon and Touch Icons  -->
    <link rel="shortcut icon" href="{{ url('/myhr/images/logo.png') }}" />
    <link rel="apple-touch-icon-precomposed" href="{{ url('/myhr/images/logo.png') }}" />
    <!-- Font -->
    <link rel="stylesheet" href="{{ url('/myhr/fonts/fonts.css') }}" />
    <!-- Icons -->
    {{--
    <link rel="stylesheet" href="{{ url('/myhr/fonts/icons-alipay.css') }}"> --}}
    <link rel="stylesheet" href="{{ url('/myhr/styles/bootstrap.css') }}">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="{{ url('adminlte/plugins/fontawesome-free/css/all.min.css') }}">
    <!-- icheck bootstrap -->

    <link rel="stylesheet" href="{{ url('adminlte/plugins/icheck-bootstrap/icheck-bootstrap.min.css') }}">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <!-- Theme style -->
    {{-- baru
    <link rel="stylesheet" href="{{ url('adminlte/dist/css/adminlte.min.css') }}"> --}}
    {{-- select picker --}}
    {{-- baru
    <link rel="stylesheet"
        href="{{ url('https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.14/dist/css/bootstrap-select.min.css') }}">
    --}}

    {{-- timepicker --}}
    {{--
    <link rel="stylesheet" href="{{ url('https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css') }}"> --}}

    <link rel="stylesheet" type="text/css" href="{{ url('/myhr/styles/styles.css') }}" />
    <link rel="manifest" href="{{ url('/myhr/_manifest.json') }}" data-pwa-version="set_in_manifest_and_pwa_js">
    <link rel="apple-touch-icon" sizes="192x192" href="{{ url('/myhr/app/icons/icon-192x192.png') }}">
    @livewireStyles

    <style>
        .select2-container {
            font-size: 14px;
        }

        .select2-container .select2-selection--single {
            height: 45px;
            line-height: 45px;
            padding-left: 7px;
        }

        .select2-container .select2-selection--single .select2-selection__rendered {
            line-height: 45px;
            /* font-size: 14px; */
            /* padding-left: 5px; */
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 45px;
            /* font-size: 14px; */
            /* padding-left: 5px; */
        }

        .select2-results__option {
            line-height: 20px;
            /* font-size: 14px; */
            /* padding-left: 5px; */
        }

        .select2-selection__choice {
            line-height: 45px;
            /* font-size: 14px; */
            /* padding-left: 5px; */
        }
    </style>
    @stack('style')
</head>

<body>
    <!-- preloade -->
    <div class="preload preload-container">
        <div class="preload-logo">
            <div class="spinner"></div>
        </div>
    </div>
    <!-- /preload -->
    <div class="mt-7 login-section">
        <div class="tf-container">
            @yield('container')
        </div>
    </div>


    <script type="text/javascript" src="{{  url('/myhr/javascript/jquery.min.js') }}"></script>
    <script type="text/javascript" src="{{  url('/myhr/javascript/bootstrap.min.js') }}"></script>
    <script type="text/javascript" src="{{  url('/myhr/javascript/password-addon.js') }}"></script>
    <script type="text/javascript" src="{{  url('/myhr/javascript/main.js') }}"></script>
    <!-- jQuery -->
    {{-- <script src="{{ url('adminlte/plugins/jquery/jquery.min.js') }}"></script> --}}

    <!-- Bootstrap 4 -->
    {{-- baru <script src="{{ url('adminlte/plugins/bootstrap/js/bootstrap.bundle.min.js') }}"></script> --}}
    <!-- AdminLTE App -->
    {{-- baru <script src="{{ url('adminlte/dist/js/adminlte.min.js') }}"></script> --}}
    {{-- selectpicker --}}
    {{-- <script
        src="{{ url('https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.14/dist/js/bootstrap-select.min.js') }}">
        --}}
    </script>
    {{-- baru <script src="{{ url('https://cdn.jsdelivr.net/npm/flatpickr') }}"></script> --}}
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>


    <script>
        $(function(){
          $('form').on('submit', function(){
            $(':input[type="submit"]').prop('disabled', true);
          })
        })
    </script>
    @stack('script')
    @livewireScripts
    @include('sweetalert::alert')

</body>

</html>