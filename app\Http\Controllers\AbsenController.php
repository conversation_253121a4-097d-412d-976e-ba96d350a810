<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Shift;
use App\Models\Lokasi;
use App\Models\Jabatan;
use App\Exports\AbsenExport;
use App\Models\MappingShift;
use Illuminate\Http\Request;
use App\Events\NotifApproval;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use RealRashid\SweetAlert\Facades\Alert;

class AbsenController extends Controller
{
    public function index()
    {
        date_default_timezone_set('Asia/Jakarta');
        $user_login = auth()->user()->id;
        $tanggal = "";
        $tglskrg = date('Y-m-d');
        $tglkmrn = date('Y-m-d', strtotime('-1 days'));

        $mapping_shift = MappingShift::select('jam_keluar', 'jam_absen', 'jam_pulang')
            ->where('user_id', $user_login)
            ->where('status_absen', '!=', 'Libur')
            ->where('status_absen', '!=', 'Izin Tidak Masuk')
            ->where('status_absen', '!=', 'Izin Sakit')
            ->where('jam_absen', '!=', null)
            ->where('tanggal', $tglkmrn)->get();

        if ($mapping_shift->count() > 0) {
            foreach ($mapping_shift as $mp) {
                $jam_absen = $mp->jam_absen;
                $jam_pulang = $mp->jam_pulang;
                $jam_keluar = $mp->jam_keluar;
            }
        } else {
            $jam_absen = "-";
            $jam_pulang = "-";
            $jam_keluar = "-";
        }
        if ($jam_pulang == null && $jam_absen != null && $jam_keluar != null && $jam_keluar <= '10:00') {
            $tanggal = $tglkmrn;
        } else {
            $tanggal = $tglskrg;
        }

        if (auth()->user()->is_admin == 'admin') {
            return redirect('/dashboard');
            // return view('absen.index', [
            //     'title' => 'Absen',
            //     'shift_karyawan' => MappingShift::where('user_id', $user_login)->where('tanggal', $tglskrg)->first()
            // ]);
        } else {
            return view('absen.indexUser', [
                'title' => 'Absen',
                'shift_karyawan' => MappingShift::select('id', 'tanggal', 'jam_masuk', 'jam_keluar', 'lock_location', 'status_absen', 'jam_absen', 'jam_pulang')
                    ->where('user_id', $user_login)
                    ->where('tanggal', $tanggal)->first(),
            ]);
        }
    }

    public function myLocation(Request $request)
    {
        return redirect('maps/' . $request["lat"] . '/' . $request['long'] . '/' . $request['userid']);
    }

    //waiting
    public function recalculateDistanceShow()
    {
        echo '<h2>Absen Masuk</h2>';
        // $mapping_shift = MappingShift::with('User')->whereNotNull('lat_absen')->whereNotNull('long_absen')->orderBy('id', 'ASC')->toSql();
        // echo $mapping_shift;
        // dd('test');
        $mapping_shift = MappingShift::whereNotNull('lat_absen')->whereNotNull('long_absen')->orderBy('id', 'ASC')->get();
        foreach ($mapping_shift as $ms) {
            echo $ms->User->name . ' | ';
            $temp_user = User::where('is_admin', 'user')->where('id', $ms->user_id)->get();
            foreach ($temp_user as $dtuser) {
                $lat_kantor = $dtuser->Lokasi->lat_kantor;
                $long_kantor = $dtuser->Lokasi->long_kantor;
                echo $dtuser->Lokasi->nama_lokasi . '<br>';
                // echo "<a href=" . url('maps/' . $ms->lat_absen . '/' . $ms->long_absen . '/' . $ms->user_id) . ">Lihat</a>";
                echo 'Absen (' . $ms->lat_absen . ',' . $ms->long_absen . ') | ';
                echo 'Kantor (' . $lat_kantor . ',' . $long_kantor . ') <br>';
                echo 'Jarak: ';
                $jarak = $this->distance($ms->lat_absen, $ms->long_absen, $lat_kantor, $long_kantor, "K") * 1000;
                echo ($jarak > $dtuser->Lokasi->radius) ? '<b>' . $jarak . '</b>' : $jarak;
                echo '<br>';

                // if ($jarak > $dtuser->Lokasi->radius) {
                //     $update["jarak_masuk"] = $jarak;
                //     $ms->update($update);
                // }
            }
            echo '<hr>';
        }

        echo '<h2>Absen Pulang</h2>';
        $mapping_shift = MappingShift::whereNotNull('lat_pulang')->whereNotNull('long_pulang')->orderBy('id', 'ASC')->get();
        foreach ($mapping_shift as $ms) {
            echo $ms->User->name . ' | ';
            $temp_user = User::where('is_admin', 'user')->where('id', $ms->user_id)->get();
            foreach ($temp_user as $dtuser) {
                $lat_kantor = $dtuser->Lokasi->lat_kantor;
                $long_kantor = $dtuser->Lokasi->long_kantor;
                echo $dtuser->Lokasi->nama_lokasi . '<br>';
                echo 'Absen (' . $ms->lat_pulang . ',' . $ms->long_pulang . ') | ';
                echo 'Kantor (' . $lat_kantor . ',' . $long_kantor . ') <br>';
                echo 'Jarak: ';
                $jarak = $this->distance($ms->lat_pulang, $ms->long_pulang, $lat_kantor, $long_kantor, "K") * 1000;
                echo ($jarak > $dtuser->Lokasi->radius) ? '<b>' . $jarak . '</b>' : $jarak;
                echo '<br>';

                // if ($jarak > $dtuser->Lokasi->radius) {
                //     $update["jarak_pulang"] = $jarak;
                //     $ms->update($update);
                // }
            }
            echo '<hr>';
        }
    }

    public function recalculateDistance(Request $request)
    {
        $request->validate([
            'lokasi_id' => 'required',
        ]);

        echo "<h2>Masuk</h2>";
        $lokasi = Lokasi::when($request->lokasi_id, function ($query) use ($request) {
            $query->where('id', $request->lokasi_id);
        })->orderBy('id', 'ASC')->get();

        foreach ($lokasi as $lok) {
            echo $lok->nama_lokasi . '<br>';

            $mapping_shift =  DB::table('mapping_shifts')
                ->whereRaw(DB::raw('user_id IN (select id from users where lokasi_id = ' . $lok->id . ' AND is_admin = "user")'))
                ->whereNotNull('lat_absen')
                ->whereNotNull('long_absen')
                ->orderBy('tanggal', 'ASC')
                ->get();

            foreach ($mapping_shift as $ms) {
                // $temp_user = User::where('is_admin', 'user')->where('id', $ms->user_id)->get();
                // foreach ($temp_user as $dtuser) {
                $lat_kantor = $lok->lat_kantor;
                $long_kantor = $lok->long_kantor;

                $jarak = $this->distance($ms->lat_absen, $ms->long_absen, $lat_kantor, $long_kantor, "K") * 1000;

                echo $ms->tanggal . '---' . $lok->nama_lokasi . ' --> Jarak: ' . $jarak . '--> Radius: ' . $lok->radius;
                // if ($jarak > $dtuser->Lokasi->radius) {
                // echo '<br>' . $ms->id . '---' . $ms->tanggal . ' --> Jarak: ' . $jarak . '<Br>';
                MappingShift::where('id', $ms->id)->where('tanggal', $ms->tanggal)->update(['jarak_masuk' => $jarak]);
                echo '---updated';
                // }
                echo '<br>';
            }
        }

        echo "<h2>Pulang</h2>";
        foreach ($lokasi as $lok) {
            echo $lok->nama_lokasi . '<br>';

            $mapping_shift =  DB::table('mapping_shifts')
                ->whereRaw(DB::raw('user_id IN (select id from users where lokasi_id = ' . $lok->id . ' AND is_admin = "user")'))
                ->whereNotNull('lat_pulang')
                ->whereNotNull('long_pulang')
                ->orderBy('tanggal', 'ASC')
                ->get();

            foreach ($mapping_shift as $ms) {
                // $temp_user = User::where('is_admin', 'user')->where('id', $ms->user_id)->get();
                // foreach ($temp_user as $dtuser) {
                $lat_kantor = $lok->lat_kantor;
                $long_kantor = $lok->long_kantor;

                $jarak = $this->distance($ms->lat_pulang, $ms->long_pulang, $lat_kantor, $long_kantor, "K") * 1000;

                echo $ms->tanggal . '---' . $lok->nama_lokasi . ' --> Jarak: ' . $jarak . '--> Radius: ' . $lok->radius;
                // if ($jarak > $dtuser->Lokasi->radius) {
                // echo '<br>' . $ms->id . '---' . $ms->tanggal . ' --> Jarak: ' . $jarak . '<Br>';
                MappingShift::where('id', $ms->id)->where('tanggal', $ms->tanggal)->update(['jarak_pulang' => $jarak]);
                // }
                echo '---updated';
                echo '<br>';
            }
        }
    }

    public function recalculateTimeAll(Request $request)
    {
        date_default_timezone_set('Asia/Jakarta');
        DB::connection('mysql')->disableQueryLog();
        set_time_limit(180);

        $request->validate([
            'mulai' => 'required',
            'sampai' => 'required',
        ]);

        $mulai = $request->mulai;
        $akhir = $request->sampai;

        if ($request->lokasi_id) {
            $lokasi_id = $request->lokasi_id;
            $this->recalculateTime($lokasi_id, null, $mulai, $akhir);
        } else {
            $lokasi = Lokasi::select('id')->orderBy('id', 'ASC')->get();
            foreach ($lokasi as $lok) {
                $lokasi_id = $lok->id;
                $this->recalculateTime($lokasi_id, null, $mulai, $akhir);
            }
        }
    }

    public static function recalculateTime($lokasi_id = null, $user_id = null, $mulai = null, $akhir = null)
    {
        // echo $user_id . '--' . $mulai . '--' . $akhir;
        // echo '<hr>';
        date_default_timezone_set('Asia/Jakarta');

        $mapping_shift = MappingShift::select('id', 'user_id', 'tanggal', 'jam_masuk', 'jam_keluar', 'jam_absen', 'jam_pulang')
            ->when($lokasi_id, function ($query) use ($lokasi_id) {
                $query->whereRaw('user_id IN (select id from users where lokasi_id = ' . $lokasi_id . ' AND is_admin = "user")');
            })
            ->when($user_id, function ($query) use ($user_id) {
                $query->where('user_id', $user_id);
            })
            ->when($mulai && $akhir, function ($query) use ($mulai, $akhir) {
                $query->whereBetween('tanggal', [$mulai, $akhir]);
            })
            ->whereRaw(DB::raw('(jam_absen is not null OR jam_pulang is not null)'))
            ->orderBy('user_id', 'ASC')
            ->orderBy('tanggal', 'ASC')
            ->get();

        // dd($mapping_shift);

        foreach ($mapping_shift as $mp) {
            echo $mp->User->name . ' | ' . $mp->tanggal;
            $j_masuk = $mp->jam_masuk;
            $j_pulang = $mp->jam_keluar;

            $a_masuk = $mp->jam_absen;
            $a_pulang = $mp->jam_pulang;

            $tanggal = $mp->tanggal;

            $count = 0;
            //absen masuk
            $telat = 0;
            $temp_masuk = null;
            if (!is_null($a_masuk)) {
                // echo $tanggal . '--' . $j_masuk . ' | ' . $a_masuk . '<br>';
                $m_awal  = strtotime($tanggal . $j_masuk);
                $m_akhir =  strtotime($tanggal . $a_masuk);
                $m_diff  = $m_akhir - $m_awal;

                $temp_masuk = $m_akhir; // sementara
                $telat = ($m_diff > 0) ? $m_diff : 0;
                $count = 1;
            }


            //absen pulang
            $cepat = 0;
            $temp_pulang = null;
            if (!is_null($a_pulang)) {
                $p_awal  = new \DateTime($tanggal . $j_pulang);
                $p_akhir =  new \DateTime($tanggal . $a_pulang);

                $temp_pulang = $p_akhir->getTimestamp(); // sementara

                if ($temp_pulang < $temp_masuk) {
                    // $xj_pulang = new \DateTime($p_awal);
                    $xa_pulang = new \DateTime($tanggal . $a_pulang);
                    $p_akhir = $xa_pulang->modify('+1 day');

                    echo $mp->User->name . ' | Masuk: ' . date('Y-m-d H:i:s', $temp_masuk) . ' | Pulang: ' . date('Y-m-d H:i:s', $temp_pulang) . ' terindikasi pulang di hari berikutnya';
                    $count = 1;
                }

                $p_diff  = $p_awal->getTimestamp() - $p_akhir->getTimestamp();

                $cepat = ($p_diff > 0) ? $p_diff : 0;
            }

            $update = [
                'telat' => $telat,
                'pulang_cepat' => $cepat,
            ];

            $updated = MappingShift::where('id', $mp->id)->update($update);
            if ($updated && $count) {
                echo " --------updated<br>";
                $count = 0;
            }
        }

        // return redirect('/data-absen')->with('success', 'Berhasil Edit Absen Pulang (Manual)');
    }

    public function generateJadwal()
    {
        $data_shift = MappingShift::select('id', 'shift_id')->orderBy('user_id')->get();
        foreach ($data_shift as $dts) {
            //echo $dts->Shift->nama_shift . '---' . $dts->Shift->jam_masuk . '---' . $dts->Shift->jam_keluar . '<br>';
            $update = [
                'jam_masuk' => $dts->Shift->jam_masuk,
                'jam_keluar' =>  $dts->Shift->jam_keluar,
            ];

            // $cek = MappingShift::findOrFail($dts->id)->update($update);
            // if ($cek) {
            //     echo 'Berhasil - ' . $dts->Shift->nama_shift . '---' . $dts->Shift->jam_masuk . '---' . $dts->Shift->jam_keluar . '<br>';
            // } else {
            //     echo 'Gagal - ' . $dts->Shift->nama_shift . '---' . $dts->Shift->jam_masuk . '---' . $dts->Shift->jam_keluar . '<br>';
            // }
        }
    }

    public function absenMasuk(Request $request, $id)
    {
        date_default_timezone_set('Asia/Jakarta');

        $lat_kantor = auth()->user()->Lokasi->lat_kantor;
        $long_kantor = auth()->user()->Lokasi->long_kantor;
        $radius = auth()->user()->Lokasi->radius;
        $nama_lokasi = auth()->user()->Lokasi->nama_lokasi;

        $request["jarak_masuk"] = $this->distance($request["lat_absen"], $request["long_absen"], $lat_kantor, $long_kantor, "K") * 1000;

        $request["jam_absen"] = date('H:i');

        $mapping_shift = MappingShift::find($id);

        if ($request["jarak_masuk"] > $radius && $mapping_shift->lock_location == 1) {
            Alert::error('Diluar Jangkauan', 'Lokasi Anda Diluar Radius ' . $nama_lokasi);
            return redirect('/absen');
        } else {
            $foto_jam_absen = $request["foto_jam_absen"];

            $image_parts = explode(";base64,", $foto_jam_absen);

            $image_base64 = base64_decode($image_parts[1]);
            $fileName = 'foto_jam_absen/' . uniqid() . '.png';

            Storage::put($fileName, $image_base64);


            $request["foto_jam_absen"] = $fileName;

            $request["status_absen"] = "Masuk";

            $shift = $mapping_shift->jam_masuk;
            $tanggal = $mapping_shift->tanggal;

            $tgl_skrg = date("Y-m-d");

            $awal  = strtotime($tanggal . $shift);
            $akhir = strtotime($tgl_skrg . $request["jam_absen"]);
            $diff  = $akhir - $awal;

            if ($diff <= 0) {
                $request["telat"] = 0;
            } else {
                $request["telat"] = $diff;
            }

            if ($mapping_shift->lock_location == 1) {
                $validatedData = $request->validate([
                    'jam_absen' => 'required',
                    'telat' => 'nullable',
                    'foto_jam_absen' => 'required',
                    'lat_absen' => 'required',
                    'long_absen' => 'required',
                    'jarak_masuk' => 'required',
                    'status_absen' => 'required'
                ]);
            } else {
                $validatedData = $request->validate([
                    'jam_absen' => 'required',
                    'telat' => 'nullable',
                    'foto_jam_absen' => 'required',
                    'lat_absen' => 'required',
                    'long_absen' => 'required',
                    'jarak_masuk' => 'required',
                    // 'keterangan_masuk' => 'required',
                    'status_absen' => 'required'
                ]);
            }

            // dd($validatedData);

            MappingShift::where('id', $id)->update($validatedData);

            $request->session()->flash('success', 'Berhasil Absen Masuk! Jangan lupa untuk Absen Pulang ya ...');

            return redirect('/my-absen');
        }
    }

    public function absenPulang(Request $request, $id)
    {
        date_default_timezone_set('Asia/Jakarta');
        $request["jam_pulang"] = date('H:i');

        $lat_kantor = auth()->user()->Lokasi->lat_kantor ?? null;
        $long_kantor = auth()->user()->Lokasi->long_kantor ?? null;
        $radius = auth()->user()->Lokasi->radius ?? null;
        $nama_lokasi = auth()->user()->Lokasi->nama_lokasi ?? null;

        $request["jarak_pulang"] = $this->distance($request["lat_pulang"], $request["long_pulang"], $lat_kantor, $long_kantor, "K") * 1000;

        $mapping_shift = MappingShift::find($id);

        if ($request["jarak_pulang"] > $radius && $mapping_shift->lock_location == 1) {
            Alert::error('Diluar Jangkauan', 'Lokasi Anda Diluar Radius ' . $nama_lokasi);
            return redirect('/absen');
        } else {
            $foto_jam_pulang = $request["foto_jam_pulang"];

            $image_parts = explode(";base64,", $foto_jam_pulang);

            $image_base64 = base64_decode($image_parts[1]);
            $fileName = 'foto_jam_pulang/' . uniqid() . '.png';

            Storage::put($fileName, $image_base64);

            $request["foto_jam_pulang"] = $fileName;


            $shiftmasuk = $mapping_shift->jam_masuk;
            $shiftpulang = $mapping_shift->jam_keluar;
            $tanggal = $mapping_shift->tanggal;
            $new_tanggal = "";
            $timeMasuk = strtotime($shiftmasuk);
            $timePulang = strtotime($shiftpulang);


            if ($timePulang < $timeMasuk) {
                $new_tanggal = date('Y-m-d', strtotime('+1 days', strtotime($tanggal)));
            } else {
                $new_tanggal = $tanggal;
            }

            $tgl_skrg = date("Y-m-d");

            $akhir = strtotime($new_tanggal . $shiftpulang);
            $awal  = strtotime($tgl_skrg . $request["jam_pulang"]);
            $diff  = $akhir - $awal;

            if ($diff <= 0) {
                $request["pulang_cepat"] = 0;
            } else {
                $request["pulang_cepat"] = $diff;
            }

            if ($mapping_shift->lock_location == 1) {
                $validatedData = $request->validate([
                    'jam_pulang' => 'required',
                    'foto_jam_pulang' => 'required',
                    'lat_pulang' => 'required',
                    'long_pulang' => 'required',
                    'pulang_cepat' => 'required',
                    'keterangan_pulang' => 'required',
                    'jarak_pulang' => 'required'
                ]);
            } else {
                $validatedData = $request->validate([
                    'jam_pulang' => 'required',
                    'foto_jam_pulang' => 'required',
                    'lat_pulang' => 'required',
                    'long_pulang' => 'required',
                    'pulang_cepat' => 'required',
                    'keterangan_pulang' => 'required',
                    'jarak_pulang' => 'required'
                ]);
            }
            $validatedData['keterangan_pulang'] = nl2br($request->keterangan_pulang);
            // dd($validatedData);
            MappingShift::where('id', $id)->update($validatedData);

            return redirect('/my-absen')->with('success', 'Berhasil Absen Pulang');
        }
    }

    public function distance($lat1, $lon1, $lat2, $lon2, $unit)
    {
        $theta = $lon1 - $lon2;
        $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) +  cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
        $dist = acos($dist);
        $dist = rad2deg($dist);
        $miles = $dist * 60 * 1.1515;
        $unit = strtoupper($unit);

        if ($unit == "K") {
            return ($miles * 1.609344);
        } else if ($unit == "N") {
            return ($miles * 0.8684);
        } else {
            return $miles;
        }
    }

    public function dataAbsen($params = null)
    {
        return view('absen.livewire-dataabsen', [
            'title' => 'Data Absen',
        ]);
    }

    public function exportDataAbsen()
    {
        // echo "abc";
        return (new AbsenExport($_GET))->download('List Absensi.xlsx');
    }

    public function maps($lat, $long, $userid)
    {
        date_default_timezone_set('Asia/Jakarta');
        if (auth()->user()->is_admin == 'admin') {
            return view('absen.maps', [
                'title' => 'Maps',
                'lat' => $lat,
                'long' => $long,
                'data_user' => User::findOrFail($userid)
            ]);
        } else {
            return view('absen.mapsUser', [
                'title' => 'Maps',
                'lat' => $lat,
                'long' => $long,
                'data_user' => User::findOrFail($userid)
            ]);
        }
    }

    public function editMasuk($id)
    {
        $mapping_shift = MappingShift::findOrFail($id);
        $user = User::findOrFail($mapping_shift->user_id);
        $lokasi = $user->Lokasi;
        return view('absen.editmasuk', [
            'title' => 'Edit Absen Masuk',
            'data_absen' => $mapping_shift,
            'lokasi_kantor' => $lokasi
        ]);
    }

    public function prosesEditMasuk(Request $request, $id)
    {
        date_default_timezone_set('Asia/Jakarta');

        $mapping_shift = MappingShift::where('id', $id)->get();

        foreach ($mapping_shift as $mp) {
            $shift = $mp->jam_masuk;
            $tanggal = $mp->tanggal;
            $user_id = $mp->user_id;
        }

        $awal  = strtotime($tanggal . $shift);
        $akhir = strtotime($tanggal . $request["jam_absen"]);
        $diff  = $akhir - $awal;

        if ($diff <= 0) {
            $request["telat"] = 0;
        } else {
            $request["telat"] = $diff;
        }

        $user = User::findOrFail($user_id);
        $lat_kantor = $user->Lokasi->lat_kantor;
        $long_kantor = $user->Lokasi->long_kantor;

        $request["jarak_masuk"] = $this->distance($request["lat_absen"], $request["long_absen"], $lat_kantor, $long_kantor, "K") * 1000;

        $validatedData = $request->validate([
            'jam_absen' => 'required',
            'telat' => 'nullable',
            'foto_jam_absen' => 'image|max:5000',
            'lat_absen' => 'required',
            'long_absen' => 'required',
            'jarak_masuk' => 'required',
            'status_absen' => 'required'
        ]);

        if ($request->file('foto_jam_absen')) {
            if ($request->foto_jam_absen_lama) {
                Storage::delete($request->foto_jam_absen_lama);
            }
            $validatedData['foto_jam_absen'] = $request->file('foto_jam_absen')->store('foto_jam_absen');
        }

        MappingShift::where('id', $id)->update($validatedData);
        return redirect('/data-absen')->with('success', 'Berhasil Edit Absen Masuk (Manual)');
    }

    public function editPulang($id)
    {
        $mapping_shift = MappingShift::findOrFail($id);
        // dd($mapping_shift);
        $user = User::findOrFail($mapping_shift->user_id);
        $lokasi = $user->Lokasi;
        return view('absen.editpulang', [
            'title' => 'Edit Absen Pulang',
            'data_absen' => $mapping_shift,
            'lokasi_kantor' => $lokasi
        ]);
    }

    public function prosesEditPulang(Request $request, $id)
    {
        $mapping_shift = MappingShift::where('id', $id)->get();
        foreach ($mapping_shift as $mp) {
            $shiftmasuk = $mp->jam_masuk;
            $shiftpulang = $mp->jam_keluar;
            $tanggal = $mp->tanggal;
            $user_id = $mp->user_id;
        }
        $new_tanggal = "";
        $timeMasuk = strtotime($shiftmasuk);
        $timePulang = strtotime($shiftpulang);


        if ($timePulang < $timeMasuk) {
            $new_tanggal = date('Y-m-d', strtotime('+1 days', strtotime($tanggal)));
        } else {
            $new_tanggal = $tanggal;
        }

        $akhir = strtotime($new_tanggal . $shiftpulang);
        $awal  = strtotime($new_tanggal . $request["jam_pulang"]);
        $diff  = $akhir - $awal;

        if ($diff <= 0) {
            $request["pulang_cepat"] = 0;
        } else {
            $request["pulang_cepat"] = $diff;
        }

        $user = User::findOrFail($user_id);
        $lat_kantor = $user->Lokasi->lat_kantor;
        $long_kantor = $user->Lokasi->long_kantor;

        $request["jarak_pulang"] = $this->distance($request["lat_pulang"], $request["long_pulang"], $lat_kantor, $long_kantor, "K") * 1000;

        $validatedData = $request->validate([
            'jam_pulang' => 'required',
            'foto_jam_pulang' => 'image|max:5000',
            'lat_pulang' => 'required',
            'long_pulang' => 'required',
            'pulang_cepat' => 'required',
            'jarak_pulang' => 'required'
        ]);

        if ($request->file('foto_jam_pulang')) {
            if ($request->foto_jam_pulang_lama) {
                Storage::delete($request->foto_jam_pulang_lama);
            }
            $validatedData['foto_jam_pulang'] = $request->file('foto_jam_pulang')->store('foto_jam_pulang');
        }

        MappingShift::where('id', $id)->update($validatedData);

        return redirect('/data-absen')->with('success', 'Berhasil Edit Absen Pulang (Manual)');
    }

    public function deleteAdmin($id)
    {
        $delete = MappingShift::find($id);
        @Storage::delete($delete->foto_jam_absen);
        @Storage::delete($delete->foto_jam_pulang);
        $delete->delete();
        return redirect('/data-absen')->with('success', 'Data Berhasil di Delete');
    }

    public function myAbsen(Request $request)
    {
        date_default_timezone_set('Asia/Jakarta');

        $tglawal = date('Y-m-01');
        $tglskrg = date('Y-m-t');

        if ($request['bulan']) {
            // $tglawal = date('Y-' . $request['bulan'] . '-01');
            // $tglskrg = date('Y-' . $request['bulan'] . '-t');

            $date = new \DateTime(date('Y') . '-' . $request['bulan'] . '-01');
            $date->modify('first day of this month');
            $tglawal = $date->format('Y-m-d');
            $date->modify('last day of this month');
            $tglskrg = $date->format('Y-m-d');
        }

        $data_absen = MappingShift::where('user_id', auth()->user()->id)->whereBetween('tanggal', [$tglawal, $tglskrg])->orderBy('tanggal', 'ASC')->get();
        // $data_absen = MappingShift::where('tanggal', $tglskrg)->where('user_id', auth()->user()->id);

        // if ($request["mulai"] == null) {
        //     $request["mulai"] = $request["akhir"];
        // }

        // if ($request["akhir"] == null) {
        //     $request["akhir"] = $request["mulai"];
        // }

        // if ($request["mulai"] && $request["akhir"]) {
        //     $data_absen = MappingShift::where('user_id', auth()->user()->id)->whereBetween('tanggal', [$request["mulai"], $request["akhir"]]);
        // }

        if (auth()->user()->is_admin == 'admin') {
            return view('absen.myabsen', [
                'title' => 'My Absen',
                'data_absen' => $data_absen
            ]);
        } else {
            return view('absen.myabsenuser', [
                'title' => 'My Absen',
                'data_absen' => $data_absen
            ]);
        }
    }

    public function pengajuan($id)
    {
        $ms = MappingShift::find($id);
        $title = 'Pengajuan Absensi';
        return view('absen.pengajuan', compact(
            'ms',
            'title'
        ));
    }

    public function pengajuanProses(Request $request, $id)
    {
        date_default_timezone_set('Asia/Jakarta');

        $ms = MappingShift::find($id);
        $validated = $request->validate([
            'jam_masuk_pengajuan' => 'required',
            'jam_pulang_pengajuan' => 'required',
            'deskripsi' => 'required',
            'file_pengajuan' => 'required',
            'status_pengajuan' => 'required',
        ]);

        if ($request->file('file_pengajuan')) {
            $validated['file_pengajuan'] = $request->file('file_pengajuan')->store('file_pengajuan');
        }

        $ms->update($validated);

        $pembimbing_id = User::find(auth()->user()->id)->pembimbing_id;
        $pembimbing = User::where('id', $pembimbing_id)->first();

        $jabatan = Jabatan::find(auth()->user()->jabatan_id);
        $user = User::find($jabatan->manager);

        $type = 'Approval';
        $notif = 'Pengajuan Absensi Dari ' . auth()->user()->name . ' Butuh Approval Anda';
        $url = url('/pengajuan-absensi/edit/' . $ms->id);

        //notif ke admin
        $user->messages = [
            'user_id'   =>  auth()->user()->id,
            'from'   =>  auth()->user()->name,
            'message'   =>  $notif,
            'action'   =>  '/pengajuan-absensi/edit/' . $ms->id
        ];
        $user->notify(new \App\Notifications\UserNotification);

        NotifApproval::dispatch($type, $user->id, $notif, $url);
        //end notif admin

        //notif ke pembimbing
        $pembimbing->messages = [
            'user_id'   =>  auth()->user()->id,
            'from'   =>  auth()->user()->name,
            'message'   =>  $notif,
            'action'   =>  '/pengajuan-absensi/edit/' . $ms->id
        ];
        $pembimbing->notify(new \App\Notifications\UserNotification);

        NotifApproval::dispatch($type, $pembimbing->id, $notif, $url);
        //end notif pembimbing

        return redirect('/pengajuan-absensi')->with('success', 'Pengajuan Berhasil Disimpan');
    }

    public function pengajuanAbsensi()
    {
        $title = 'Pengajuan Absensi';
        $search = request()->input('search');

        //sebagai admin
        if (auth()->user()->is_admin == 'admin') {
            $mulai = request()->input('mulai');
            $akhir = request()->input('akhir');

            $status = request()->input('status');

            $mapping_shift = MappingShift::where('status_pengajuan', '!=', null)
                ->when($search, function ($query) use ($search) {
                    $query->whereHas('User', function ($query) use ($search) {
                        $query->where('name', 'LIKE', '%' . $search . '%');
                    });
                })
                ->when($mulai && $akhir, function ($query) use ($mulai, $akhir) {
                    $query->whereBetween('tanggal', [$mulai, $akhir]);
                })
                ->when($status, function ($query) use ($status) {
                    $query->where('status_pengajuan', $status);
                })
                ->orderBy('tanggal', 'DESC')
                ->paginate(20)
                ->withQueryString();

            return view('absen.indexPengajuanAdmin', compact(
                'mapping_shift',
                'title',
            ));
        }

        //sebagai guru atau siswa
        $jabatan = Jabatan::find(auth()->user()->jabatan_id);
        $user_id = User::where('jabatan_id', auth()->user()->jabatan_id)->pluck('id');

        $user_pembimbing = User::where('pembimbing_id', auth()->user()->id)->pluck('id');

        $mapping_shift = MappingShift::where('status_pengajuan', '!=', null)
            ->when($jabatan->manager == auth()->user()->id, function ($query) use ($user_id) {
                $query->where(function ($q) use ($user_id) {
                    $q->whereIn('user_id', $user_id)
                        ->orWhere('user_id', auth()->user()->id);
                });
            })
            ->when(count($user_pembimbing) > 0, function ($query) use ($user_pembimbing) {
                $query->where(function ($q) use ($user_pembimbing) {
                    $q->whereIn('user_id', $user_pembimbing);
                });
            })
            ->when(count($user_pembimbing) <= 0, function ($query) {
                $query->where('user_id', auth()->user()->id);
            })
            ->when($search, function ($query) use ($search) {
                $query->whereHas('User', function ($query) use ($search) {
                    $query->where('name', 'LIKE', '%' . $search . '%');
                });
            })
            ->orderBy('tanggal', 'DESC')
            ->paginate(10)
            ->withQueryString();

        return view('absen.indexPengajuan', compact(
            'mapping_shift',
            'title'
        ));
    }

    public function editPengajuanAbsensi($id)
    {
        $ms = MappingShift::find($id);

        if (auth()->user()->is_admin == 'admin') {
            $title = 'Edit Pengajuan Absensi';
            return view('absen.editPengajuanAdmin', compact(
                'ms',
                'title'
            ));
        }

        $jabatan = Jabatan::find(auth()->user()->jabatan_id);
        $pembimbing_id = User::find($ms->user_id)->pembimbing_id;

        $title = 'Pengajuan Absensi';
        return view('absen.editPengajuan', compact(
            'ms',
            'jabatan',
            'pembimbing_id',
            'title'
        ));
    }

    public function updatePengajuanAbsensi(Request $request, $id)
    {
        $ms = MappingShift::find($id);
        $validated = $request->validate([
            'jam_masuk_pengajuan' => 'required',
            'jam_pulang_pengajuan' => 'required',
            'deskripsi' => 'required',
            'file_pengajuan' => 'nullable',
            'komentar' => 'nullable',
            'status_pengajuan' => 'required',
        ]);

        $ms->update($validated);

        if ($request['status_pengajuan'] == 'Disetujui') {
            $shiftmasuk = $ms->jam_masuk;
            $tanggal = $ms->tanggal;

            $awal_masuk  = strtotime($tanggal . $shiftmasuk);
            $akhir_masuk = strtotime($tanggal . $ms->jam_masuk_pengajuan);
            $diff_masuk  = $akhir_masuk - $awal_masuk;

            if ($diff_masuk <= 0) {
                $telat = 0;
            } else {
                $telat = $diff_masuk;
            }

            $shiftpulang = $ms->jam_keluar;
            $new_tanggal = "";
            $timeMasuk = strtotime($shiftmasuk);
            $timePulang = strtotime($shiftpulang);

            if ($timePulang < $timeMasuk) {
                $new_tanggal = date('Y-m-d', strtotime('+1 days', strtotime($tanggal)));
            } else {
                $new_tanggal = $tanggal;
            }

            $akhir_pulang = strtotime($new_tanggal . $shiftpulang);
            $awal_pulang  = strtotime($new_tanggal . $ms->jam_pulang_pengajuan);
            $diff_pulang  = $akhir_pulang - $awal_pulang;

            if ($diff_pulang <= 0) {
                $pulang_cepat = 0;
            } else {
                $pulang_cepat = $diff_pulang;
            }

            $ms->update([
                'jam_absen' => $ms->jam_masuk_pengajuan,
                'telat' => $telat,
                'lat_absen' => $ms->User->Lokasi->lat_kantor,
                'long_absen' => $ms->User->Lokasi->long_kantor,
                'jarak_masuk' => 0,
                'jam_pulang' => $ms->jam_pulang_pengajuan,
                'pulang_cepat' => $pulang_cepat,
                'lat_pulang' => $ms->User->Lokasi->lat_kantor,
                'long_pulang' => $ms->User->Lokasi->long_kantor,
                'jarak_pulang' => 0,
                'status_absen' => 'Masuk',
            ]);

            $user = User::find($ms->user_id);

            $type = 'Approved';
            $notif = 'Pengajuan Absensi Anda Telah Di Setujui Oleh ' . auth()->user()->name;
            $url = url('/pengajuan-absensi/edit/' . $ms->id);

            $user->messages = [
                'user_id'   =>  auth()->user()->id,
                'from'   =>  auth()->user()->name,
                'message'   =>  $notif,
                'action'   =>  '/pengajuan-absensi/edit/' . $ms->id
            ];
            $user->notify(new \App\Notifications\UserNotification);

            NotifApproval::dispatch($type, $user->id, $notif, $url);
        } else if ($request['status_pengajuan'] == 'Tidak Disetujui') {
            $user = User::find($ms->user_id);

            $type = 'Rejected';
            $notif = 'Pengajuan Absensi Anda Tidak Setujui Oleh ' . auth()->user()->name;
            $url = url('/pengajuan-absensi/edit/' . $ms->id);

            $user->messages = [
                'user_id'   =>  auth()->user()->id,
                'from'   =>  auth()->user()->name,
                'message'   =>  $notif,
                'action'   =>  '/pengajuan-absensi/edit/' . $ms->id
            ];
            $user->notify(new \App\Notifications\UserNotification);

            NotifApproval::dispatch($type, $user->id, $notif, $url);
        } else {
            $jabatan = Jabatan::find(auth()->user()->jabatan_id);
            $user = User::find($jabatan->manager);

            $type = 'Approval';
            $notif = 'Pengajuan Absensi Dari ' . auth()->user()->name . ' Butuh Approval Anda';
            $url = url('/pengajuan-absensi/edit/' . $ms->id);

            //notif ke admin
            $user->messages = [
                'user_id'   =>  auth()->user()->id,
                'from'   =>  auth()->user()->name,
                'message'   =>  $notif,
                'action'   =>  '/pengajuan-absensi/edit/' . $ms->id
            ];
            $user->notify(new \App\Notifications\UserNotification);

            NotifApproval::dispatch($type, $user->id, $notif, $url);

            //notif ke pembimbing
            $pembimbing_id = User::find(auth()->user()->id)->pembimbing_id;
            $pembimbing = User::where('id', $pembimbing_id)->first();

            $pembimbing->messages = [
                'user_id'   =>  auth()->user()->id,
                'from'   =>  auth()->user()->name,
                'message'   =>  $notif,
                'action'   =>  '/pengajuan-absensi/edit/' . $ms->id
            ];
            $pembimbing->notify(new \App\Notifications\UserNotification);

            NotifApproval::dispatch($type, $pembimbing->id, $notif, $url);
        }

        return redirect('/pengajuan-absensi')->with('success', 'Pengajuan Berhasil Diupdate');
    }

    public function rekapPresensiSiswa(Request $request)
    {
        date_default_timezone_set('Asia/Jakarta');

        $tglawal = date('Y-m-d');
        $tglskrg = date('Y-m-d');

        if ($request['tanggal'] && $request['bulan']) {
            if ($request['tanggal'] != 'semua') {
                $tglawal = date('Y-' . $request['bulan'] . '-' . $request['tanggal']);
                $tglskrg = date('Y-' . $request['bulan'] . '-' . $request['tanggal']);
            } else {
                $date = new \DateTime(date('Y') . '-' . $request['bulan'] . '-01');
                $date->modify('first day of this month');
                $tglawal = $date->format('Y-m-d');
                $date->modify('last day of this month');
                $tglskrg = $date->format('Y-m-d');
            }
        }

        $user_id = auth()->user()->id;
        $siswa_bimbingan = User::select('id', 'name')->where('pembimbing_id', $user_id)
            ->orderBy('lokasi_id', 'ASC')
            ->orderBy('name', 'ASC')->get();

        if ($request->siswa) {
            $siswa_bimbingan = User::select('id', 'name')->where('pembimbing_id', $user_id)
                ->where('id', $request->siswa)->get();
        }

        $data_absen = [];
        foreach ($siswa_bimbingan as $dsb) {
            $data_presensi = MappingShift::select('tanggal', 'jam_absen', 'jam_pulang', 'status_absen', 'telat', 'pulang_cepat')
                ->where('user_id', $dsb->id)
                ->whereBetween('tanggal', [$tglawal, $tglskrg])
                ->orderBy('tanggal', 'ASC')
                ->get();

            $data_absen[$dsb->id] = [
                'identitas' => $dsb,
                'presensi' => $data_presensi,
            ];
        }
        return view('absen.myabsensiswa', [
            'title' => 'Presensi Siswa Bimbingan',
            'data_absen' => $data_absen,
            'data_siswa' => User::select('id', 'name')
                ->where('pembimbing_id', $user_id)
                ->orderBy('lokasi_id', 'ASC')
                ->orderBy('name', 'ASC')->get(),
        ]);
    }

    public function presensiBulananSiswa(Request $request)
    {
        date_default_timezone_set('Asia/Jakarta');

        $tglawal = date('Y-m-01');
        $tglskrg = date('Y-m-d');

        if ($request['bulan'] && $request['bulan'] == date('m')) {
            $tglawal = date('Y-' . $request['bulan'] . '-01');
            $tglskrg = date('Y-' . $request['bulan'] . '-' . date('d'));
        } elseif ($request['bulan'] && $request['bulan'] != date('m')) {
            $date = new \DateTime(date('Y') . '-' . $request['bulan'] . '-01');
            $date->modify('first day of this month');
            $tglawal = $date->format('Y-m-d');
            $date->modify('last day of this month');
            $tglskrg = $date->format('Y-m-d');
        }

        $user_id = auth()->user()->id;
        $siswa_bimbingan = User::select('id', 'name')->where('pembimbing_id', $user_id)
            ->orderBy('lokasi_id', 'ASC')
            ->orderBy('name', 'ASC')->get();

        if ($request->siswa) {
            $siswa_bimbingan = User::select('id', 'name')->where('pembimbing_id', $user_id)
                ->where('id', $request->siswa)->get();
        }

        $data_absen = [];
        foreach ($siswa_bimbingan as $dsb) {
            $data_presensi = MappingShift::select('tanggal', 'jam_absen', 'jam_pulang', 'status_absen', 'telat', 'pulang_cepat')
                ->where('user_id', $dsb->id)
                ->whereBetween('tanggal', [$tglawal, $tglskrg])
                ->orderBy('tanggal', 'ASC')
                ->get();

            $data_absen[$dsb->id] = [
                'identitas' => $dsb,
                'presensi' => $data_presensi,
            ];
        }
        return view('absen.rekapabsensiswa', [
            'title' => 'Rekap Presensi Siswa',
            'data_absen' => $data_absen,
            'data_siswa' => User::select('id', 'name')
                ->where('pembimbing_id', $user_id)
                ->orderBy('lokasi_id', 'ASC')
                ->orderBy('name', 'ASC')->get(),
        ]);
    }

    public function unduhPresensiPdf(Request $request)
    {
        date_default_timezone_set('Asia/Jakarta');
        ini_set('max_execution_time', 300);

        $tglawal = date('Y-m-01');
        $tglskrg = date('Y-m-d');

        if ($request['bulan'] && $request['bulan'] == date('m')) {
            $tglawal = date('Y-' . $request['bulan'] . '-01');
            $tglskrg = date('Y-' . $request['bulan'] . '-' . date('d'));
        } elseif ($request['bulan'] && $request['bulan'] != date('m')) {
            $date = new \DateTime(date('Y') . '-' . $request['bulan'] . '-01');
            $date->modify('first day of this month');
            $tglawal = $date->format('Y-m-d');
            $date->modify('last day of this month');
            $tglskrg = $date->format('Y-m-d');
        }

        $user_id = auth()->user()->id;
        $siswa_bimbingan = User::select('users.id', 'name', 'gender', 'lokasis.nama_lokasi', 'jurusans.kode_jurusan')
            ->join('lokasis', 'lokasis.id', '=', 'users.lokasi_id')
            ->join('jurusans', 'jurusans.id', '=', 'lokasis.jurusan_id')->where('pembimbing_id', $user_id)
            ->orderBy('lokasi_id', 'ASC')
            ->orderBy('name', 'ASC')->get();

        if ($request->siswa) {
            $siswa_bimbingan = User::select('users.id', 'name', 'lokasis.nama_lokasi', 'jurusans.kode_jurusan')
                ->join('lokasis', 'lokasis.id', '=', 'users.lokasi_id')
                ->join('jurusans', 'jurusans.id', '=', 'lokasis.jurusan_id')
                ->where('pembimbing_id', $user_id)
                ->where('id', $request->siswa)->get();
        }

        $data_absen = [];
        foreach ($siswa_bimbingan as $dsb) {
            $data_presensi = MappingShift::select('tanggal', 'jam_absen', 'jam_pulang', 'status_absen', 'telat', 'pulang_cepat')
                ->where('user_id', $dsb->id)
                ->whereBetween('tanggal', [$tglawal, $tglskrg])
                ->orderBy('tanggal', 'ASC')
                ->get();

            $data_absen[$dsb->id] = [
                'identitas' => $dsb,
                'presensi' => $data_presensi,
            ];
        }

        $pdf = Pdf::loadView('absen.rekapabsensiswapdf', [
            'title' => 'Rekap Presensi Siswa',
            'data_absen' => $data_absen,
            'pembimbing' => User::select('name', 'nomor_induk')->where('id', $user_id)->first(),
            'data_siswa' => User::select('id', 'name')
                ->where('pembimbing_id', $user_id)
                ->orderBy('lokasi_id', 'ASC')
                ->orderBy('name', 'ASC')->get(),
        ]);
        $customPaper = [0, 0, 595.55, 935.55];
        $pdf->setPaper($customPaper, 'landscape');
        return $pdf->stream('REKAP PRESENSI SISWA.pdf');
    }

    public function libur($id)
    {
        $pa = MappingShift::find($id);
        $update = [
            'jam_masuk' => "00:00",
            'jam_keluar' => "00:00",
            'status_absen' => 'Libur',
        ];

        MappingShift::where('user_id', $pa->user_id)->where('tanggal', $pa->tanggal)->update($update);
        $pa->update(['status_pengajuan' => 'Disetujui']);
        return redirect('/pengajuan-absensi')->with('success', 'Data Berhasil di Update');
    }

    public function deletePengajuanAbsensi($id)
    {
        $data = MappingShift::find($id);
        if ($data->file_pengajuan) {
            Storage::delete($data->file_pengajuan);
        }
        $update = [
            'jam_masuk_pengajuan' => null,
            'jam_pulang_pengajuan' => null,
            'deskripsi' => null,
            'file_pengajuan' => null,
            'status_pengajuan' => null,
            'komentar' => null,
        ];
        MappingShift::where('id', $id)->update($update);
        // MappingShift::where('id', $id)->where('status_absen', '!=', 'Tidak Masuk')->update($update);
        return redirect('/pengajuan-absensi')->with('success', 'Data Pengajuan Absensi berhasil dihapus');
    }


    public function absenByStatus(Request $request)
    {
        $tanggal = $request->tanggal;
        $status = $request->status;
        $data = MappingShift::select('user_id', 'status_absen')
            ->where('tanggal', $tanggal)
            ->where('status_absen', $status)
            ->paginate(20)
            ->withQueryString();

        return view('absen.absenbystatus', [
            'title' => 'Siswa ' . $status . ' Tanggal ' . $tanggal,
            'data' => $data,
        ]);
    }
}


// 03-08-2025
// Perubahan:
// $shift_karyawan->Shift->jam_masuk ---> $shift_karyawan->jam_masuk
// $shift_karyawan->Shift->jam_keluar ---> $shift_karyawan->jam_keluar
