<div>
    <div class="row mb-2 mb-xl-3">
        <div class="col-auto d-none d-sm-block">
            <h3>Dashboard</h3>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-lg-2 col-sm-4 col-md-3 col-xxl-2 mb-2 mb-sm-0">
                            <input type="datetime" class="form-control" wire:model="mulai" placeholder="Mulai"
                                id="mulai">
                        </div>
                        <div class="col-lg-2 col-sm-4 col-md-3 col-xxl-2 mb-2 mb-sm-0">
                            <input type="datetime" class="form-control" wire:model="akhir" placeholder="Sampai"
                                id="akhir">
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped my-0">
                        <thead>
                            <tr>
                                <th>No.</th>
                                <th>Tanggal</th>
                                <th>Ter<PERSON><PERSON>wal</th>
                                <th>Masuk</th>
                                <th>Pulang</th>
                                <th>Libur</th>
                                <th>Sakit</th>
                                <th>Izin TM</th>
                                <th>Izin T</th>
                                <th>Izin PC</th>
                                <th>Pending</th>
                                <th>Alpha</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php $no = 1; @endphp
                            @foreach($info_absen as $ia)
                            <tr>
                                <td width="50">{{ $no++ }}</td>
                                <td>{{ $ia->tanggal }}</td>
                                <td>{{ $ia->terjadwal }}</td>
                                <td class="text-success fw-bold">{{ $ia->masuk }}</td>
                                <td class="text-success fw-bold">{{ $ia->pulang }}</td>
                                <td>
                                    <a class="text-info fw-bold text-decoration-none"
                                        href="{{ url('/absen?tanggal='.$ia->tanggal.'&status=Libur') }}">{{ $ia->libur
                                        }}</a>
                                </td>
                                <td>
                                    <a class="text-warning fw-bold text-decoration-none"
                                        href="{{ url('/absen?tanggal='.$ia->tanggal.'&status=Izin Sakit') }}">{{
                                        $ia->izin_sakit }}</a>
                                </td>
                                <td>
                                    <a class="text-warning fw-bold text-decoration-none"
                                        href="{{ url('/absen?tanggal='.$ia->tanggal.'&status=Izin Tidak Masuk') }}">{{
                                        $ia->izin_tidak_masuk }}</a>
                                </td>
                                <td>
                                    <a class="text-warning fw-bold text-decoration-none"
                                        href="{{ url('/absen?tanggal='.$ia->tanggal.'&status=Izin Telat') }}">{{
                                        $ia->izin_telat }}</a>
                                </td>
                                <td>
                                    <a class="text-warning fw-bold text-decoration-none"
                                        href="{{ url('/absen?tanggal='.$ia->tanggal.'&status=Izin Pulang Cepat') }}">{{
                                        $ia->izin_pulang_cepat }}</a>
                                </td>
                                <td>
                                    <a class="text-primary fw-bold text-decoration-none"
                                        href="{{ url('/cuti?tanggal='.$ia->tanggal.'&status=Pending') }}">{{
                                        $info_pengajuan[$ia->tanggal] ?? 0 }}</a>
                                </td>
                                <td>
                                    <a class="text-danger fw-bold text-decoration-none"
                                        href="{{ url('/absen?tanggal='.$ia->tanggal.'&status=Tidak Masuk') }}">{{
                                        $ia->alpha }}</a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 col-lg-5">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Presensi Siswa PKL Hari Ini</h5>
                    <h6 class="card-subtitle text-muted">Data diambil tanggal <b>{{ date('d-m-Y') }}</b></h6>
                </div>
                <div class="card-body">
                    <div class="chart w-100">
                        <div id="apexcharts-pie"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-lg-7">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Grafik Presensi</h5>
                    <h6 class="card-subtitle text-muted">Periode {{ $mulai }} s/d {{ $akhir }}</h6>
                </div>
                <div class="card-body">
                    <div class="chart w-100">
                        <div id="apexcharts-area"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 col-sm-6 col-xxl-3 d-flex">
            <div class="card illustration flex-fill">
                <div class="card-body p-0 d-flex flex-fill">
                    <div class="row g-0 w-100">
                        <div class="col-6">
                            <div class="p-3 m-1">
                                <h4>Siswa PKL</h4>
                                <p class="mb-0">{{ $jumlah_user }} Siswa</p>
                            </div>
                        </div>
                        <div class="col-6 align-self-end text-end">
                            <img src="{{ url('/adminkit/img/illustrations/customer-support.png') }}"
                                alt="Customer Support" class="img-fluid illustration-img">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-xxl-3 d-flex">
            <div class="card illustration flex-fill">
                <div class="card-body p-0 d-flex flex-fill">
                    <div class="row g-0 w-100">
                        <div class="col-6">
                            <div class="p-3 m-1">
                                <h4>Belum Mapping</h4>
                                <p class="mb-0">{{ $jumlah_belum_mapping }} Siswa</p>
                            </div>
                        </div>
                        <div class="col-6 align-self-end text-end">
                            <img src="{{ url('/adminkit/img/illustrations/social-media.png') }}" alt="Social Media"
                                class="img-fluid illustration-img">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>