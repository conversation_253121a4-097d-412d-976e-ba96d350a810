<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Jabatan;
use App\Models\Jurusan;
use Illuminate\Http\Request;
use RealRashid\SweetAlert\Facades\Alert;

class jabatanController extends Controller
{
    public function index()
    {
        $search = request()->input('search');
        $jabatan = Jabatan::when($search, function ($query) use ($search) {
            $query->where('nama_jabatan', 'LIKE', '%' . $search . '%');
        })
            // ->where('nama_jabatan', 'LIKE', '%XII%')
            ->orderBy('nama_jabatan', 'ASC')
            ->paginate(10)
            ->withQueryString();

        return view('jabatan.index', [
            'title' => 'Kelas',
            'data_jabatan' => $jabatan
        ]);
    }

    public function create()
    {
        return view('jabatan.create', [
            'title' => 'Tambah Data Kelas',
            'data_jurusan' => Jurusan::orderBy('id', 'ASC')->get(),
            'users' => User::select('id', 'name')->where('is_admin', 'admin')->orderBy('name')->get(),
        ]);
    }

    public function insert(Request $request)
    {
        $validatedData = $request->validate([
            'nama_jabatan' => 'required|max:255',
            'jurusan_id' => 'required',
            'manager' => 'nullable',
        ]);

        Jabatan::create($validatedData);
        return redirect('/jabatan')->with('success', 'Data Berhasil di Tambahkan');
    }

    public function edit($id)
    {
        return view('jabatan.edit', [
            'title' => 'Edit Data Kelas',
            'data_jabatan' => Jabatan::findOrFail($id),
            'data_jurusan' => Jurusan::orderBy('id', 'ASC')->get(),
            'users' => User::select('id', 'name')->where('is_admin', 'admin')->orderBy('name')->get(),
        ]);
    }

    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'nama_jabatan' => 'required|max:255',
            'jurusan_id' => 'required',
            'manager' => 'nullable',
        ]);

        Jabatan::where('id', $id)->update($validatedData);
        return redirect('/jabatan')->with('success', 'Data Berhasil di Update');
    }

    public function delete($id)
    {
        $jabatan = Jabatan::findOrFail($id);
        $user = User::where('jabatan_id', $id)->count();
        if ($user > 0) {
            Alert::error('Failed', 'Masih Ada User Yang Menggunakan Kelas Ini!');
            return redirect('/jabatan');
        } else {
            $jabatan->delete();
            return redirect('/jabatan')->with('success', 'Data Berhasil di Delete');
        }
    }
}
