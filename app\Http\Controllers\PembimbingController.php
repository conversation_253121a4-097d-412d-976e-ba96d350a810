<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Cuti;
use App\Models\User;
use App\Models\Shift;
use App\Models\Lokasi;
use App\Models\Jabatan;
use App\Models\AutoShift;
use App\Models\MappingShift;
use Illuminate\Http\Request;
use App\Events\NotifApproval;
use Illuminate\Support\Facades\Storage;
use RealRashid\SweetAlert\Facades\Alert;
use App\Http\Controllers\AbsenController;

class PembimbingController extends Controller
{
    public function penerjunan()
    {
        // Alert::info('Informasi', 'Masa penerjunan siswa PKL sudah selesai!');
        return redirect('/dashboard')->with('info', 'Masa penerjunan siswa PKL sudah selesai!');
        $user_id = auth()->user()->id;
        $user = User::findOrFail(auth()->user()->id);

        $mulai = request()->input('mulai');
        $akhir = request()->input('akhir');

        $cuti = Cuti::where('user_id', $user_id)
            ->when($mulai && $akhir, function ($query) use ($mulai, $akhir) {
                return $query->whereBetween('tanggal', [$mulai, $akhir]);
            })
            ->orderBy('id', 'desc')->paginate(10)->withQueryString();

        $data_lokasi = [];
        if (auth()->user()->jurusan_id) {
            $data_lokasi = Lokasi::where('status', 'approved')
                ->where('jurusan_id', '=', auth()->user()->jurusan_id)
                ->get();
        } else {
            $data_lokasi = Lokasi::where('status', 'approved')->get();
        }

        return view('pembimbing.indexguru', [
            'title' => 'Penerjunan PKL',
            'data_user' => $user,
            'data_cuti_user' => $cuti,
            'data_lokasi' => $data_lokasi
        ]);
    }

    public function penerjunanSimpan(Request $request)
    {
        date_default_timezone_set('Asia/Jakarta');

        $data_lokasi = [
            'lat_kantor' => $request->lat,
            'long_kantor' => $request->long,
            'updated_by' => auth()->user()->id,
        ];

        Lokasi::where('id', $request->lokasi_id)->update($data_lokasi);

        $validatedData = $request->validate([
            'lokasi_id' => 'required',
            // 'nama_shift' => 'required',
            'tanggal_mulai' => 'required',
            'tanggal_akhir' => 'required',
            'jam_masuk' => 'required',
            'jam_keluar' => 'required',
            'lat' => 'required',
            'long' => 'required'
        ]);

        $data_shift = [
            'nama_shift' => Lokasi::where('id', $request->lokasi_id)->first()->nama_lokasi,
            'jam_masuk' => $request->jam_masuk,
            'jam_keluar' => $request->jam_keluar
        ];

        $data_shift['created_by'] = auth()->user()->id;

        $shift = Shift::create($data_shift);

        $shift_id = $shift->id;


        if ($request["tanggal_mulai"] == null) {
            $request["tanggal_mulai"] = $request["tanggal_akhir"];
        } else {
            $request["tanggal_mulai"] = $request["tanggal_mulai"];
        }

        if ($request["tanggal_akhir"] == null) {
            $request["tanggal_akhir"] = $request["tanggal_mulai"];
        } else {
            $request["tanggal_akhir"] = $request["tanggal_akhir"];
        }

        $begin = new \DateTime($request["tanggal_mulai"]);
        // $end = new \DateTime($request["tanggal_akhir"]);
        // $end = new \DateTime($request["tanggal_mulai"]);
        $end = new \DateTime("2025-08-10");
        //$end = $end->modify('+11 day');

        $end1 = $end->modify('+1 day');

        $interval = new \DateInterval('P1D'); //referensi : https://en.wikipedia.org/wiki/ISO_8601#Durations
        $daterange = new \DatePeriod($begin, $interval, $end1);

        $auto_shift = [
            'lokasi_id' => $request->lokasi_id,
            'shift_id' => $shift_id,
            'tanggal_mulai' => $begin->format('Y-m-d'),
            'tanggal_akhir' => $end->format('Y-m-d'),
        ];

        AutoShift::create($auto_shift);

        $user = User::where('lokasi_id', $request->lokasi_id)
            ->where('is_admin', 'user')
            ->select(['id', 'name'])
            ->get();


        $bulk_insert = [];
        foreach ($daterange as $date) {
            $tanggal = $date->format("Y-m-d");

            foreach ($user as $datauser) {
                MappingShift::where('user_id', $datauser->id)
                    ->where('status_absen', 'Tidak Masuk')
                    ->orWhere('status_absen', 'Libur')
                    ->whereNull('jam_absen')
                    ->whereNull('jam_pulang')
                    ->delete();

                $cek = MappingShift::where('user_id', $datauser->id)
                    ->where('tanggal', $tanggal)->first();

                if (!$cek) {
                    if ($request["shift_id"] == 1) {
                        $request["status_absen"] = "Libur";
                    } else {
                        $request["status_absen"] = "Tidak Masuk";
                    }

                    $request["tanggal"] = $tanggal;

                    $validatedData = [
                        'user_id' => $datauser->id,
                        'shift_id' => $shift_id,
                        'tanggal' => $tanggal,
                        'status_absen' => $request["status_absen"],
                    ];

                    // $validatedData['lock_location'] = $request['lock_location'] ? $request['lock_location'] : null;
                    $validatedData['lock_location'] = 0;
                    $validatedData['telat'] = 0;
                    $validatedData['pulang_cepat'] = 0;
                    // $validatedData['created_at'] = Carbon::now();
                    // $validatedData['updated_at'] = Carbon::now();

                    //MappingShift::create($validatedData);
                    $bulk_insert[] = $validatedData;
                }
            }
        }
        MappingShift::insert($bulk_insert);
        return redirect('/dashboard')->with('success', 'Penerjunan PKL berhasil');
    }

    public function bimbinganSiswa($id)
    {
        // dd("test" . $id);
        $search = request()->input('search');

        $data = User::when($search, function ($query) use ($search) {
            $query->where('name', 'LIKE', '%' . $search . '%')
                ->orWhere('email', 'LIKE', '%' . $search . '%')
                ->orWhere('telepon', 'LIKE', '%' . $search . '%')
                ->orWhere('username', 'LIKE', '%' . $search . '%')
                ->orWhereHas('Jabatan', function ($query) use ($search) {
                    $query->where('nama_jabatan', 'LIKE', '%' . $search . '%');
                })
                ->orWhereHas('Lokasi', function ($query) use ($search) {
                    $query->where('nama_lokasi', 'LIKE', '%' . $search . '%');
                });
        })
            ->where('is_admin', 'user')
            ->where('pembimbing_id', $id)
            ->orderBy('name', 'ASC')
            ->paginate(20)
            ->withQueryString();

        if (auth()->user()->is_admin == 'admin') {
            return view('pembimbing.bimbingansiswa', [
                'title' => 'Siswa Bimbingan dari: ' . User::find($id)->name,
                'data_user' => $data,
                'pembimbing' => User::find($id)->id,
                'jabatan' => Jabatan::select('id', 'nama_jabatan')->where('id', '!=', '1')->get(),
            ]);
        }
    }

    public function bimbinganSiswaUser()
    {
        // dd("test" . $id);
        $id = auth()->user()->id;
        $data = User::where('is_admin', 'user')
            ->where('pembimbing_id', $id)
            ->orderBy('lokasi_id', 'ASC')
            ->orderBy('name', 'ASC')
            ->get();

        return view('pembimbing.bimbingansiswauser', [
            'title' => 'Data Siswa Bimbingan',
            'data_user' => $data,
        ]);
    }

    public function tambahSiswa($id)
    {
        $data = User::where('is_admin', 'user')
            ->whereNull('pembimbing_id')
            ->orderBy('name', 'ASC')
            ->get();

        if (auth()->user()->is_admin == 'admin') {
            return view('pembimbing.tambahsiswa', [
                'title' => 'Tambah Siswa Bimbingan dari: ' . User::find($id)->name,
                'data_user' => $data,
                'pembimbing' => User::find($id)->id,
                'jabatan' => Jabatan::select('id', 'nama_jabatan')->where('id', '!=', '1')->get(),
            ]);
        }
    }

    public function tambahSiswaProses($pembimbing, $siswa)
    {
        $update = [
            'pembimbing_id' => $pembimbing,
        ];
        User::where('id', $siswa)->update($update);
        return redirect('/pembimbing/siswa/tambah/' . $pembimbing)->with('success', 'Data Berhasil Ditambahkan');
    }

    public function keluarkanSiswa($pembimbing, $siswa)
    {
        $update = [
            'pembimbing_id' => null,
        ];
        User::where('id', $siswa)->update($update);
        return redirect('/pembimbing/siswa/' . $pembimbing)->with('success', 'Data Berhasil Dikeluarkan');
    }

    public function lihatJurnal(Request $request)
    {
        date_default_timezone_set('Asia/Jakarta');

        $tglawal = date('Y-m-d');
        $tglskrg = date('Y-m-d');

        if ($request['tanggal'] && $request['bulan']) {
            if ($request['tanggal'] != 'semua') {
                $tglawal = date('Y-' . $request['bulan'] . '-' . $request['tanggal']);
                $tglskrg = date('Y-' . $request['bulan'] . '-' . $request['tanggal']);
            } else {
                $tglawal = date('Y-' . $request['bulan'] . '-01');
                $tglskrg = date('Y-' . $request['bulan'] . '-t');
            }
        }

        $user_id = auth()->user()->id;
        $siswa_bimbingan = User::select('id', 'name')->orderBy('lokasi_id', 'ASC')->orderBy('name', 'ASC')
            ->where('pembimbing_id', $user_id)->get();

        if ($request->siswa) {
            $siswa_bimbingan = User::select('id', 'name')->where('pembimbing_id', $user_id)->where('id', $request->siswa)->get();
        }
        // if ($request["mulai"] == null) {
        //     $request["mulai"] = $request["akhir"];
        // }

        // if ($request["akhir"] == null) {
        //     $request["akhir"] = $request["mulai"];
        // }

        // print_r($request['mulai'] . '--' . $request['akhir']);

        $data_absen = [];
        foreach ($siswa_bimbingan as $dsb) {
            // print_r($dsb->name);
            // echo '<hr>';
            $data_presensi = MappingShift::select('tanggal', 'jam_pulang', 'status_absen', 'keterangan_pulang')
                ->where('user_id', $dsb->id)
                ->whereBetween('tanggal', [$tglawal, $tglskrg])
                ->get();
            // if ($request["mulai"] && $request["akhir"]) {
            //     $data_presensi = MappingShift::select('tanggal', 'jam_absen', 'jam_pulang', 'status_absen')->where('user_id', $dsb->id)->whereBetween('tanggal', [$request["mulai"], $request["akhir"]])->get();
            // }
            $data_absen[$dsb->id] = [
                'identitas' => $dsb,
                'presensi' => $data_presensi,
            ];
        }
        return view('pembimbing.myjurnalsiswa', [
            'title' => 'Jurnal Kegiatan Siswa',
            'data_absen' => $data_absen,
            'data_siswa' => User::select('id', 'name')->orderBy('lokasi_id', 'ASC')->orderBy('name', 'ASC')
                ->where('pembimbing_id', $user_id)->get()
        ]);
    }

    public function lihatJadwal(Request $request)
    {
        date_default_timezone_set('Asia/Jakarta');

        $date = new \DateTime();
        $date->modify('monday this week');
        $tglawal =  $date->format('Y-m-d');

        $date->modify('sunday this week');
        $tglskrg =  $date->format('Y-m-d');

        $user_id = auth()->user()->id;
        $siswa_bimbingan = User::select('id', 'name')->orderBy('lokasi_id', 'ASC')->orderBy('name', 'ASC')
            ->where('pembimbing_id', $user_id)->get();

        if ($request->siswa) {
            $siswa_bimbingan = User::select('id', 'name')->where('pembimbing_id', $user_id)->where('id', $request->siswa)->get();
        }
        // if ($request["mulai"] == null) {
        //     $request["mulai"] = $request["akhir"];
        // }

        // if ($request["akhir"] == null) {
        //     $request["akhir"] = $request["mulai"];
        // }

        // print_r($request['mulai'] . '--' . $request['akhir']);
        $data_jadwal = [];
        foreach ($siswa_bimbingan as $dsb) {
            // print_r($dsb->name);
            // echo '<hr>';
            $jadwal = MappingShift::select('tanggal', 'jam_masuk', 'jam_keluar', 'status_absen')->where('user_id', $dsb->id)->whereBetween('tanggal', [$tglawal, $tglskrg])->get();
            // if ($request["mulai"] && $request["akhir"]) {
            //     $data_presensi = MappingShift::select('tanggal', 'jam_absen', 'jam_pulang', 'status_absen')->where('user_id', $dsb->id)->whereBetween('tanggal', [$request["mulai"], $request["akhir"]])->get();
            // }
            $data_jadwal[$dsb->id] = [
                'identitas' => $dsb,
                'jadwal' => $jadwal,
            ];
        }
        return view('pembimbing.jadwalmingguan', [
            'title' => 'Jadwal Mingguan Siswa',
            'data_jadwal' => $data_jadwal,
            'data_siswa' => User::select('id', 'name')->orderBy('lokasi_id', 'ASC')->orderBy('name', 'ASC')
                ->where('pembimbing_id', $user_id)->get(),
        ]);
    }

    public function editJadwal($id)
    {
        date_default_timezone_set('Asia/Jakarta');
        $date = new \DateTime();
        $date->modify('monday this week');
        $tglawal =  $date->format('Y-m-d');

        $date->modify('sunday this week');
        $tglskrg =  $date->format('Y-m-d');

        $jadwal = MappingShift::select('id', 'tanggal', 'jam_masuk', 'jam_keluar', 'status_absen')
            ->where('user_id', $id)
            ->whereBetween('tanggal', [$tglawal, $tglskrg])
            ->orderBy('tanggal', 'ASC')
            ->get();

        $user = User::find($id);
        return view('pembimbing.editjadwalmingguan', [
            'title' => 'Edit Jadwal Mingguan Siswa',
            'user' => $user,
            'jadwal' => $jadwal,
        ]);
    }

    public function updateJadwal(Request $request, $id)
    {
        date_default_timezone_set('Asia/Jakarta');

        $request->validate([
            'user_id' => 'required',
            // 'lokasi_id' => 'required',
            'tanggal_mulai' => 'required',
            'tanggal_akhir' => 'required',
            'senin' => 'required',
            'senin_masuk' => 'required_if:senin,1',
            'senin_pulang' => 'required_if:senin,1',
            'selasa' => 'required',
            'selasa_masuk' => 'required_if:selasa,1',
            'selasa_pulang' => 'required_if:selasa,1',
            'rabu' => 'required',
            'rabu_masuk' => 'required_if:rabu,1',
            'rabu_pulang' => 'required_if:rabu,1',
            'kamis' => 'required',
            'kamis_masuk' => 'required_if:kamis,1',
            'kamis_pulang' => 'required_if:kamis,1',
            'jumat' => 'required',
            'jumat_masuk' => 'required_if:jumat,1',
            'jumat_pulang' => 'required_if:jumat,1',
            'sabtu' => 'required',
            'sabtu_masuk' => 'required_if:sabtu,1',
            'sabtu_pulang' => 'required_if:sabtu,1',
            'minggu' => 'required',
            'minggu_masuk' => 'required_if:minggu,1',
            'minggu_pulang' => 'required_if:minggu,1',
        ]);

        $jadwal = [
            'Mon' => ['jadwal' => $request->senin],
            'Tue' => ['jadwal' => $request->selasa],
            'Wed' => ['jadwal' => $request->rabu],
            'Thu' => ['jadwal' => $request->kamis],
            'Fri' => ['jadwal' => $request->jumat],
            'Sat' => ['jadwal' => $request->sabtu],
            'Sun' => ['jadwal' => $request->minggu],
        ];

        if ($request->senin == 1) {
            $jadwal['Mon']['masuk'] = $request->senin_masuk;
            $jadwal['Mon']['pulang'] = $request->senin_pulang;
        } else {
            $jadwal['Mon']['masuk'] = null;
            $jadwal['Mon']['pulang'] = null;
        }
        if ($request->selasa == 1) {
            $jadwal['Tue']['masuk'] = $request->selasa_masuk;
            $jadwal['Tue']['pulang'] = $request->selasa_pulang;
        } else {
            $jadwal['Tue']['masuk'] = null;
            $jadwal['Tue']['pulang'] = null;
        }
        if ($request->rabu == 1) {
            $jadwal['Wed']['masuk'] = $request->rabu_masuk;
            $jadwal['Wed']['pulang'] = $request->rabu_pulang;
        } else {
            $jadwal['Wed']['masuk'] = null;
            $jadwal['Wed']['pulang'] = null;
        }
        if ($request->kamis == 1) {
            $jadwal['Thu']['masuk'] = $request->kamis_masuk;
            $jadwal['Thu']['pulang'] = $request->kamis_pulang;
        } else {
            $jadwal['Thu']['masuk'] = null;
            $jadwal['Thu']['pulang'] = null;
        }
        if ($request->jumat == 1) {
            $jadwal['Fri']['masuk'] = $request->jumat_masuk;
            $jadwal['Fri']['pulang'] = $request->jumat_pulang;
        } else {
            $jadwal['Fri']['masuk'] = null;
            $jadwal['Fri']['pulang'] = null;
        }
        if ($request->sabtu == 1) {
            $jadwal['Sat']['masuk'] = $request->sabtu_masuk;
            $jadwal['Sat']['pulang'] = $request->sabtu_pulang;
        } else {
            $jadwal['Sat']['masuk'] = null;
            $jadwal['Sat']['pulang'] = null;
        }
        if ($request->minggu == 1) {
            $jadwal['Sun']['masuk'] = $request->minggu_masuk;
            $jadwal['Sun']['pulang'] = $request->minggu_pulang;
        } else {
            $jadwal['Sun']['masuk'] = null;
            $jadwal['Sun']['pulang'] = null;
        }
        //$jadwal = json_encode($jadwal);

        $begin = new \DateTime($request->tanggal_mulai);
        $end = new \DateTime($request->tanggal_akhir);
        $end = $end->modify('+1 day');

        $interval = new \DateInterval('P1D'); //referensi : https://en.wikipedia.org/wiki/ISO_8601#Durations
        $daterange = new \DatePeriod($begin, $interval, $end);

        // dd($jadwal);
        $bulk_insert = [];
        foreach ($daterange as $date) {
            $tanggal = $date->format("Y-m-d");
            $cek = MappingShift::where('user_id', $request->user_id)
                ->where('tanggal', $tanggal)->first();

            $harian = $jadwal[$date->format('D') . ""];
            if (!$cek) {
                $status_absen = '';
                if ($harian["jadwal"] == 2) {
                    $status_absen = "Libur";
                } else {
                    $status_absen = "Tidak Masuk";
                }
                $datajadwal = [
                    'user_id' => $request->user_id,
                    'tanggal' => $tanggal,
                    'jam_masuk' => ($status_absen == "Libur") ? "00:00" : $harian["masuk"],
                    'jam_keluar' => ($status_absen == "Libur") ? "00:00" : $harian["pulang"],
                    'status_absen' => $status_absen,
                ];
                $bulk_insert[] = $datajadwal;
            } else {
                // echo $tanggal . '----> ' . $cek->jam_masuk . '---' . $cek->jam_keluar . '<br>';
                // print_r($harian);
                // echo '<hr>';
                //dd($harian);
                $status_absen = '';
                if ($harian['jadwal'] == 2) {
                    $status_absen = 'Libur';
                } else {
                    $status_absen = 'Tidak Masuk';
                }
                // echo $status_absen . '---------------';
                if ($cek->status_absen != 'Libur' && $cek->status_absen != 'Tidak Masuk') {
                    $status_absen = $cek->status_absen;
                }

                if ($request->timpa_jadwal == 1 && $harian['jadwal'] == 2) {
                    $status_absen = 'Libur';
                }

                // echo '-->' . ($status_absen == 'Libur') ? 'xxxlibur' : 'xxxlain' . '--->' . $harian['masuk'] . '--' . $harian['pulang'] . '---><br>';
                $datajadwal = [
                    'jam_masuk' => ($status_absen == 'Libur') ? "00:00" : $harian['masuk'],
                    'jam_keluar' => ($status_absen == 'Libur') ? "00:00" : $harian['pulang'],
                    'status_absen' => $status_absen,
                ];
                // echo "update:";
                // print_r($datajadwal);
                // echo '<hr>';

                MappingShift::where('user_id', $request->user_id)
                    ->where('tanggal', $tanggal)->update($datajadwal);
            }

            // print_r($harian);
            // echo $tanggal . '<br>';
        }
        MappingShift::insert($bulk_insert);

        AbsenController::recalculateTime(null, $request->user_id, $request->tanggal_mulai, $request->tanggal_akhir);

        // $request["tanggal"] = $tanggal;

        // $validatedData = [
        //     'user_id' => $datauser->id,
        //     'shift_id' => $shift_id,
        //     'tanggal' => $tanggal,
        //     'status_absen' => $request["status_absen"],
        // ];

        // // $validatedData['lock_location'] = $request['lock_location'] ? $request['lock_location'] : null;
        // $validatedData['lock_location'] = 0;
        // $validatedData['telat'] = 0;
        // $validatedData['pulang_cepat'] = 0;
        // // $validatedData['created_at'] = Carbon::now();
        // // $validatedData['updated_at'] = Carbon::now();

        // //MappingShift::create($validatedData);
        // $bulk_insert[] = $validatedData;
        // echo $request->user_id;
        // dd($bulk_insert);
        // dd($jadwal);
        return redirect('/jadwal-mingguan')->with('success', 'Jadwal Mingguan dari ' . User::find($request->user_id)->name . ' berhasil diupdate');
    }
}
