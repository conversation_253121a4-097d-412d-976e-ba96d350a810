<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class NotificationController extends Controller
{
    public function index()
    {
        date_default_timezone_set("Asia/Jakarta");
        $filter = request()->input('filter', '');
        $title = 'Notifications';

        $inboxs = auth()->user()->notifications()
            ->when($filter == 'read', fn($query) => $query->whereNotNull('read_at'))
            ->when($filter == 'unread', fn($query) => $query->whereNull('read_at'))
            ->paginate(20)
            ->withQueryString();

        // dd($inboxs);
        if (auth()->user()->is_admin == 'admin') {
            return view('notifications.indexAdmin', compact(
                'inboxs',
                'title'
            ));
        } else if (auth()->user()->is_admin == 'guru') {
            return view('notifications.indexGuru', compact(
                'inboxs',
                'title'
            ));
        } else {
            return view('notifications.index', compact(
                'inboxs',
                'title'
            ));
        }
    }

    public function read()
    {
        $filter = request()->input('filter', '');
        $title = 'Notifications';

        $inboxs = auth()->user()->notifications()
            ->whereNotNull('read_at')
            ->paginate(20)
            ->withQueryString();

        return view('notifications.indexAdmin', compact(
            'inboxs',
            'title'
        ));
    }

    public function unread()
    {
        $filter = request()->input('filter', '');
        $title = 'Notifications';

        $inboxs = auth()->user()->notifications()
            ->whereNull('read_at')
            ->paginate(20)
            ->withQueryString();

        return view('notifications.indexAdmin', compact(
            'inboxs',
            'title'
        ));
    }

    public function readMessage($id)
    {
        date_default_timezone_set("Asia/Jakarta");

        $notifikasi = auth()->user()->notifications()->where('id', $id)->whereNull('read_at')->first();
        @$notifikasi->markAsRead();
        //update notif for all users
        $notif = DB::table('notifications')->where('id', $notifikasi->id)->first();
        DB::table('notifications')->whereNull('read_at')->where('data', $notif->data)->update(['read_at' => $notifikasi->read_at]);
        //end update
        return redirect($notifikasi->data["action"]);
    }
}
