<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MappingShift extends Model
{
    use HasFactory;
    protected $guarded = ['id'];

    public function User()
    {
        return $this->belongsTo(User::class);
    }

    public function Shift()
    {
        return $this->belongsTo(Shift::class);
    }

    public static function dataAbsen()
    {
        date_default_timezone_set('Asia/Jakarta');
        $tglskrg = date('Y-m-d');

        $user_id = request()->input('user_id');
        $jabatan_id = request()->input('jabatan_id');
        $lokasi_id = request()->input('lokasi_id');

        $mulai = request()->input('mulai');
        $akhir = request()->input('akhir');

        $bulan = request()->input('bulan');
        // $terjadwal = request()->input('terjadwal');

        $data_absen = MappingShift::select('mapping_shifts.*', 'users.name', 'users.jabatan_id', 'users.lokasi_id')
            ->rightJoin('users', function ($join) use ($tglskrg) {
                $join->on('users.id', '=', 'mapping_shifts.user_id')
                    ->where('mapping_shifts.tanggal', '=', $tglskrg);
                // ->where('users.jabatan_id', '=', '1');
            })
            ->when(auth()->user()->is_admin == 'user', function ($query) {
                return $query->where('users.id', auth()->user()->id);
            })
            ->when($user_id, function ($query) use ($user_id) {
                return $query->where('users.id', $user_id);
            })
            ->when($jabatan_id, function ($query) use ($jabatan_id) {
                return $query->where('users.jabatan_id', $jabatan_id);
            })
            ->when($lokasi_id, function ($query) use ($lokasi_id) {
                return $query->where('users.lokasi_id', $lokasi_id);
            })
            ->when($mulai && $akhir, function ($query) use ($mulai, $akhir, $user_id, $jabatan_id, $lokasi_id) {
                return MappingShift::select('mapping_shifts.*', 'users.name', 'users.jabatan_id', 'users.lokasi_id')
                    ->rightJoin('users', function ($join) use ($mulai, $akhir) {
                        $join->on('users.id', '=', 'mapping_shifts.user_id')
                            ->whereBetween('tanggal', [$mulai, $akhir]);
                    })
                    ->when($jabatan_id, function ($query) use ($jabatan_id) {
                        return $query->where('users.jabatan_id', $jabatan_id);
                    })
                    ->when($lokasi_id, function ($query) use ($lokasi_id) {
                        return $query->where('users.lokasi_id', $lokasi_id);
                    })
                    ->when($user_id, function ($query) use ($user_id) {
                        return $query->where('users.id', $user_id);
                    });
            })
            ->when($bulan, function ($query) use ($bulan, $user_id) {
                return MappingShift::select('mapping_shifts.*', 'users.name', 'users.jabatan_id', 'users.lokasi_id')
                    ->rightJoin('users', function ($join) use ($bulan) {
                        $join->on('users.id', '=', 'mapping_shifts.user_id')
                            ->whereMonth('tanggal', $bulan);
                    })
                    ->when($user_id, function ($query) use ($user_id) {
                        return $query->where('users.id', $user_id);
                    });
            })
            // ->when($terjadwal, function ($query) use ($terjadwal) {
            //     if ($terjadwal == '0') {
            //         return $query->whereNull('mapping_shifts.jam_absen');
            //     }
            // })
            ->where('users.is_admin', 'user')
            ->orderBy('users.name', 'ASC')
            ->orderBy('mapping_shifts.tanggal', 'ASC');
        // dd($data_absen);
        return $data_absen;
    }
}
