<?php

namespace App\Exports;

use App\Models\User;
use App\Models\SurveyLokasi;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

class DudiExport extends DefaultValueBinder implements WithCustomValueBinder, FromQuery, WithColumnFormatting, WithMapping, WithHeadings, ShouldAutoSize, WithStyles
{
    use Exportable;
    public $no = 0;

    public function bindValue(Cell $cell, $value)
    {
        if (is_numeric($value)) {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);

            return true;
        }

        // else return default behavior
        return parent::bindValue($cell, $value);
    }

    public function styles(Worksheet $sheet)
    {
        $highestColumn = $sheet->getHighestColumn();
        $highestRow = $sheet->getHighestRow();

        //BORDER
        $sheet->getStyle("A1:$highestColumn" . $highestRow)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        // HEADER
        $sheet->getStyle("A1:" . $highestColumn . "1")->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

        // WRAP TEXT
        $sheet->getStyle("A1:$highestColumn" . $highestRow)->getAlignment()->setWrapText(true);

        // ALIGNMENT TEXT
        $sheet->getStyle("A1:$highestColumn" . $highestRow)->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_TOP);

        //BOLD FIRST ROW
        return [
            // Style the first row as bold text.
            1    => ['font' => ['bold' => true]],
        ];
    }

    public function headings(): array
    {
        return [
            'No.',
            'Jurusan',
            'Nama Dudi - Aplikasi',
            'Nama Dudi - Isian',
            'NIB',
            'Unit Kerja',
            'Alamat Jalan',
            'Alamat Dusun',
            'Alamat Desa',
            'Alamat Kecamatan',
            'Alamat Kabupaten',
            'Telepon',
            'Email',
            'Website',
            'NPWP',
            'Nama Pimpinan',
            'NIP Pimpinan',
            'Jabatan Pimpinan',
            'Nama Pembimbing',
            'NIP Pembimbing',
            'Jabatan Pembimbing',
            'Telepon Pembimbing',
            'Email Pembimbing',
        ];
    }

    public function map($model): array
    {
        $alamat = json_decode($model->alamat, true);
        $pimpinan = json_decode($model->pimpinan, true);
        $pembimbing = json_decode($model->pembimbing, true);
        return [
            ++$this->no,
            $model->Lokasi->Jurusan->nama_jurusan,
            $model->Lokasi->nama_lokasi,
            $model->nama_dudi,
            $model->nib,
            $model->unit_kerja,
            $alamat['jalan'],
            $alamat['dusun'],
            $alamat['desa'],
            $alamat['kecamatan'],
            $alamat['kabupaten'],
            $model->telepon,
            $model->email,
            $model->website,
            $model->npwp,
            $pimpinan['nama'],
            $pimpinan['nip'],
            $pimpinan['jabatan'],
            $pembimbing['nama'],
            $pembimbing['nip'],
            $pembimbing['jabatan'],
            $pembimbing['telepon'],
            $pembimbing['email'],
        ];
    }

    public function columnFormats(): array
    {
        return [];
    }

    public function query()
    {
        $search = request()->input('search');

        $data = SurveyLokasi::when($search, function ($query) use ($search) {
            $query->whereHas('User', function ($query) use ($search) {
                $query->where('name', 'LIKE', '%' . $search . '%');
            })
                ->orwhereHas('Lokasi', function ($query) use ($search) {
                    $query->where('nama_lokasi', 'LIKE', '%' . $search . '%');
                });
        })
            ->orderBy('lokasi_id', 'ASC')->orderBy('user_id', 'ASC');
        return $data;
    }
}
