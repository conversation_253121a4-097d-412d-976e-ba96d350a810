<div>
    <div class="row mb-2 mb-xl-3">
        <div class="col-auto">
            <h3>Siswa</h3>
        </div>

        <div class="col-auto ms-auto text-end mt-n1">
            <a href="{{ url('/pegawai/tambah-pegawai') }}" class="btn btn-primary">
                <i class="fa fa-plus"></i><span class="d-none d-md-inline"> Tambah</span>
            </a>

            <a href="{{ url('/pegawai/export') }}{{ request()->getQueryString() ? '?' . request()->getQueryString() : '' }}"
                class="btn btn-warning">
                <i class="fa fa-file-export"></i><span class="d-none d-md-inline"> Export</span>
            </a>

            <button class="btn btn-success" type="button" data-bs-toggle="modal" data-original-title="test"
                data-bs-target="#exampleModal">
                <i class="fas fa-file-import"></i><span class="d-none d-md-inline"> Import</span>
            </button>

            <!-- Import Modal -->
            <div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
                aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="exampleModalLabel">Import Users</h5>
                            <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <form action="{{ url('/pegawai/import') }}" method="POST" enctype="multipart/form-data">
                            <div class="modal-body">
                                @csrf
                                <div class="form-group">
                                    <label for="file_excel">File Excel</label>
                                    <input type="file" name="file_excel" id="file_excel"
                                        class="form-control @error('file_excel') is-invalid @enderror">
                                    @error('file_excel')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                    @enderror
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Close</button>
                                <button class="btn btn-secondary" type="submit">Import Data</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-lg-4 col-sm-12 col-md-6 col-xxl-4 mb-2 mb-lg-0">
                            <input type="text" placeholder="Keyword ..." class="form-control" wire:model="search">
                        </div>
                        <div class="col-lg-2 col-sm-12 col-md-4 col-xxl-2 mb-2 mb-lg-0">
                            <button type="button" wire:click="resetFilters" class="btn btn-light">
                                <i class="fas fa-sync"></i> Reset
                            </button>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-lg-3 col-sm-12 col-md-6 col-xxl-3 mb-2 mb-lg-0">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" wire:model="pembimbing" value="1"
                                    id="pembimbing">
                                <label class="form-check-label" for="pembimbing">
                                    Belum Ada Pembimbing
                                </label>
                            </div>
                        </div>
                        <div class="col-lg-3 col-sm-12 col-md-6 col-xxl-3 mb-2 mb-lg-0">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" wire:model="login" value="1" id="login">
                                <label class="form-check-label" for="login">
                                    Belum Pernah Login
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped my-0">
                        <thead>
                            <tr>
                                <th>No.</th>
                                <th>Foto</th>
                                <th>Nama</th>
                                <th>Username</th>
                                <th>Kelas</th>
                                <th>Lokasi PKL</th>
                                <th>Telepon</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php $no = ($data_user->currentPage() - 1) * $data_user->perPage() + 1; @endphp
                            @forelse($data_user as $du)
                            <tr>
                                <td width="50">{{ $no++ }}</td>
                                <td width="50">
                                    @if($du->foto_karyawan)
                                    <img src="{{ url('storage/'.$du->foto_karyawan) }}" alt="foto" width="40"
                                        height="40" class="rounded-circle">
                                    @else
                                    <img src="{{ url('adminkit/img/avatars/avatar.jpg') }}" alt="foto" width="40"
                                        height="40" class="rounded-circle">
                                    @endif
                                </td>
                                <td>{{ $du->name }}</td>
                                <td>{{ $du->username }}</td>
                                <td>{{ $du->Jabatan->nama_jabatan ?? '-' }}</td>
                                <td>{{ $du->Lokasi->nama_lokasi ?? '-' }}</td>
                                <td>{{ $du->telepon }}</td>
                                <td>
                                    @if($du->last_login)
                                    <span class="badge bg-success">Aktif</span>
                                    @else
                                    <span class="badge bg-warning">Belum Login</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-light dropdown-toggle" type="button"
                                            data-bs-toggle="dropdown">
                                            Aksi
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item"
                                                    href="{{ url('/pegawai/detail/'.$du->id) }}">Detail</a></li>
                                            <li><a class="dropdown-item"
                                                    href="{{ url('/pegawai/shift/'.$du->id) }}">Jadwal</a></li>
                                            <li><a class="dropdown-item"
                                                    href="{{ url('/pegawai/edit-password/'.$du->id) }}">Password</a>
                                            </li>
                                            <li><a class="dropdown-item"
                                                    href="{{ url('/pegawai/face/'.$du->id) }}">Wajah</a></li>
                                            <li>
                                                <hr class="dropdown-divider">
                                            </li>
                                            <li>
                                                <button class="dropdown-item text-danger"
                                                    onclick="confirm('Yakin ingin menghapus data ini?') || event.stopImmediatePropagation()"
                                                    wire:click="deleteEmployee({{ $du->id }})">
                                                    Hapus
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="9" class="text-center">Tidak ada data</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <div class="card-footer">
                    {{ $data_user->links() }}
                </div>
            </div>
        </div>
    </div>

    @if (session()->has('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    @endif
</div>