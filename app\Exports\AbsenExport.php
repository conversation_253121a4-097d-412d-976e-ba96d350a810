<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use App\Models\MappingShift;

class AbsenExport implements FromQuery, WithColumnFormatting, WithMapping, WithHeadings, ShouldAutoSize, WithStyles
{
    use Exportable;

    public function styles(Worksheet $sheet)
    {
        $highestColumn = $sheet->getHighestColumn();
        $highestRow = $sheet->getHighestRow();

        //BORDER
        $sheet->getStyle("A1:$highestColumn" . $highestRow)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        // HEADER
        $sheet->getStyle("A1:" . $highestColumn . "1")->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

        //CONTENT
        $sheet->getStyle("B:G")->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
        $sheet->getColumnDimension("B")->setAutoSize(false)->setWidth(15);
        $sheet->getColumnDimension("C")->setAutoSize(false)->setWidth(15);
        $sheet->getColumnDimension("D")->setAutoSize(false)->setWidth(15);
        $sheet->getColumnDimension("E")->setAutoSize(false)->setWidth(15);
        $sheet->getColumnDimension("F")->setAutoSize(false)->setWidth(15);
        $sheet->getColumnDimension("G")->setAutoSize(false)->setWidth(15);
        $sheet->getColumnDimension("H")->setAutoSize(false)->setWidth(50);
        $sheet->getColumnDimension("I")->setAutoSize(true);

        // WRAP TEXT
        $sheet->getStyle("A1:$highestColumn" . $highestRow)->getAlignment()->setWrapText(true);

        // ALIGNMENT TEXT
        $sheet->getStyle("A1:$highestColumn" . $highestRow)->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_TOP);

        //BOLD FIRST ROW
        return [
            // Style the first row as bold text.
            1    => ['font' => ['bold' => true]],
        ];
    }


    public function headings(): array
    {
        return [
            'Nama Siswa',
            'Tanggal',
            'Jadwal',
            'Jam Masuk',
            'Telat',
            // 'Keterangan Masuk',
            'Jam Pulang',
            'Pulang Cepat',
            'Keterangan Pulang',
            'Status Absen',
        ];
    }

    public function map($model): array
    {
        $telat = $model->telat;
        $jam   = floor($telat / (60 * 60));
        $menit = $telat - ($jam * (60 * 60));
        $menit2 = floor($menit / 60);
        $detik = $telat % 60;
        if ($jam <= 0 && $menit2 <= 0) {
            $late = '-';
        } else {
            $late = str_pad($jam, 2, '0', STR_PAD_LEFT) . ':' . str_pad($menit2, 2, '0', STR_PAD_LEFT);
            //$jam . ':' . $menit2 . ':' . $detik . ' Detik';
        }

        $pulang_cepat = $model->pulang_cepat;
        $jam_pulang_cepat   = floor($pulang_cepat / (60 * 60));
        $menit_pulang_cepat = $pulang_cepat - ($jam_pulang_cepat * (60 * 60));
        $menit_pulang_cepat2 = floor($menit_pulang_cepat / 60);
        $detik_pulang_cepat = $pulang_cepat % 60;

        if ($jam_pulang_cepat <= 0 && $menit_pulang_cepat2 <= 0) {
            $quick_return = '-';
        } else {
            $quick_return = str_pad($jam_pulang_cepat, 2, '0', STR_PAD_LEFT) . ':' . str_pad($menit_pulang_cepat2, 2, '0', STR_PAD_LEFT);
            // $jam_pulang_cepat . ':' . $menit_pulang_cepat2 . ':' . $detik_pulang_cepat;
        }

        if ($model->jam_masuk && $model->jam_keluar) {
            $shift_name = $model->jam_masuk . ' - ' . $model->jam_keluar;
        } else {
            $shift_name = '-';
        }

        return [
            $model->name,
            $model->tanggal ?? '-',
            $shift_name,
            $model->jam_absen ?? '-',
            $late,
            // $model->keterangan_masuk,
            $model->jam_pulang ?? '-',
            $quick_return,
            str_replace('<br>', ' ', strip_tags($model->keterangan_pulang)),
            $model->status_absen,
        ];
    }

    public function columnFormats(): array
    {
        return [];
    }

    public function query()
    {
        return MappingShift::dataAbsen();
    }
}
