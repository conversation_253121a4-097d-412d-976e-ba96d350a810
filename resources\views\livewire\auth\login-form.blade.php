<div>
    <form class="tf-form mb-4" wire:submit.prevent="login">
        <h1 class="text-center">Log In</h1>

        <div class="group-input">
            <label>Username</label>
            <input type="text" placeholder="Username" class="@error('username') is-invalid @enderror"
                wire:model.defer="username" {{ $isLoading ? 'disabled' : '' }}>
            @error('username')
            <div class="invalid-feedback">
                {{ $message }}
            </div>
            @enderror
        </div>

        <div class="group-input auth-pass-input last">
            <label>Password</label>
            <input type="{{ $showPassword ? 'text' : 'password' }}"
                class="password-input @error('password') is-invalid @enderror" placeholder="Password"
                wire:model.defer="password" {{ $isLoading ? 'disabled' : '' }}>
            <a class="icon-eye password-addon" wire:click="togglePassword" style="cursor: pointer;">
                <i class="fas {{ $showPassword ? 'fa-eye-slash' : 'fa-eye' }}"></i>
            </a>
            @error('password')
            <div class="invalid-feedback">
                {{ $message }}
            </div>
            @enderror
        </div>

        <div class="group-input mt-4">
            <div class="icheck-primary">
                <input type="checkbox" wire:model="remember" id="remember" {{ $isLoading ? 'disabled' : '' }}>
                <label for="remember" class="mt-2">
                    Ingat saya
                </label>
            </div>
        </div>

        <button type="submit" class="tf-btn btn-primary" {{ $isLoading ? 'disabled' : '' }}>
            @if($isLoading)
            <i class="fas fa-spinner fa-spin"></i> Loading...
            @else
            Log In
            @endif
        </button>
    </form>

    <div class="mb-2 text-center">
        <p class="mb-0 fw-3 text-center" style="font-size: 120%;">
            Lupa password?
            <a href="{{ url('/reset-password') }}" class="auth-link-rg">Reset Password</a>
        </p>
    </div>

    <hr class="mt-2 mb-2">

    <div class="text-center">
        <p class="mb-0 fw-3">Belum punya akun?
            <a href="{{ url('/register') }}" class="auth-link-rg">Daftar</a>
        </p>
    </div>

    <script>
        // Auto-focus on username field when component loads
        document.addEventListener('livewire:load', function () {
            document.querySelector('input[wire\\:model\\.defer="username"]').focus();
        });

        // Handle enter key press
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !@this.isLoading) {
                @this.call('login');
            }
        });
    </script>
</div>