<?php

namespace App\Http\Controllers;

use App\Models\Pengumuman;
use Illuminate\Http\Request;

class PengumumanController extends Controller
{
    //
    public function index()
    {
        $search = request()->input('search');
        $pengumuman = Pengumuman::when($search, function ($query) use ($search) {
            $query->where('pesan', 'LIKE', '%' . $search . '%');
            $query->orWhere('kepada', 'LIKE', '%' . $search . '%');
            // $query->orWhere('tanggal_akhir', 'LIKE', '%' . $search . '%');
        })
            ->orderBy('tanggal_mulai', 'DESC')
            ->paginate(20)->withQueryString();
        return view('pengumuman.index', [
            'title' => 'Pengumuman',
            'pengumuman' => $pengumuman
        ]);
    }

    public function create()
    {
        return view('pengumuman.create', [
            'title' => 'Tambah Pengumuman',
        ]);
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'kepada' => 'required',
            'pesan' => 'required',
            'tanggal_mulai' => 'required',
            'tanggal_akhir' => 'required',
            'status' => 'required',
            'sifat' => 'required',
            'user_id' => 'required',
        ]);

        // $validatedData['pesan'] = strip_tags($validatedData['pesan']);

        Pengumuman::create($validatedData);
        return redirect('/pengumuman')->with('success', 'Data Berhasil di Tambahkan');
    }
    public function edit($id)
    {
        $data_pengumuman = Pengumuman::findOrFail($id);
        return view('pengumuman.edit', [
            'title' => 'Edit Pengumuman',
            'data_pengumuman' => $data_pengumuman
        ]);
    }

    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'kepada' => 'required',
            'pesan' => 'required',
            'tanggal_mulai' => 'required',
            'tanggal_akhir' => 'required',
            'status' => 'required',
            'sifat' => 'required',
            'user_id' => 'required',
        ]);
        // $validatedData['pesan'] = strip_tags($validatedData['pesan']);
        Pengumuman::where('id', $id)->update($validatedData);
        return redirect('/pengumuman')->with('success', 'Data Berhasil di Update');
    }

    public function delete($id)
    {
        Pengumuman::where('id', $id)->delete();
        return redirect('/pengumuman')->with('success', 'Data Berhasil di Hapus');
    }
}
