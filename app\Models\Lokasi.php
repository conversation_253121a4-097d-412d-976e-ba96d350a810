<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Lokasi extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    public function User()
    {
        return $this->hasMany(User::class);
    }

    public function countUser()
    {
        return $this->hasMany(User::class)->where('is_admin', 'user')->count();
    }

    public function AutoShift()
    {
        return $this->hasMany(AutoShift::class);
    }

    public function Jurusan()
    {
        return $this->belongsTo(Jurusan::class);
    }

    public function CreatedBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function UpdatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function SurveyShift()
    {
        return $this->hasMany(SurveyShift::class);
    }
}
