<?php

namespace App\Imports;

use App\Models\User;
use App\Models\Lokasi;
use App\Models\Jabatan;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class UsersImportCustom implements ToModel, WithHeadingRow
{
    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        $jabatan = Jabatan::where('nama_jabatan', $row['jabatan'])->first();

        if ($jabatan) {
            $jabatan_id = $jabatan->id;
        } else {
            $jabatan_new = Jabatan::create([
                'nama_jabatan' => $row['jabatan']
            ]);
            $jabatan_id = $jabatan_new->id;
        }

        $lokasi = Lokasi::where('nama_lokasi', $row['lokasi'])->first();
        if ($lokasi) {
            $lokasi_id = $lokasi->id;
        } else {
            $lokasi_new = Lokasi::create([
                'nama_lokasi' => $row['lokasi'],
                'created_by' => auth()->user()->id,
                'status' => 'approved',
            ]);

            $lokasi_id = $lokasi_new->id;
        }
        return new User([
            "name" => Str::upper($row["name"]),
            "email" => $row["email"],
            "telepon" => $row["telepon"],
            "username" => $row["username"],
            "password" => Hash::make($row['password']),
            "tgl_lahir" => $row["tanggal_lahir"],
            "gender" => $row["gender"],
            "alamat" => $row['alamat'],
            "is_admin" => $row['role'],
            "jabatan_id" => $jabatan_id,
            "lokasi_id" => $lokasi_id,
        ]);
    }
}
