<?php

namespace App\Http\Livewire\Leave;

use App\Models\Cuti;
use App\Models\User;
use App\Models\MappingShift;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Storage;

class LeaveManagement extends Component
{
    use WithPagination;

    public $status_filter = '';
    public $user_filter = '';
    public $mulai = '';
    public $akhir = '';
    public $perPage = 20;

    // Modal properties
    public $showModal = false;
    public $selectedLeave = null;
    public $approval_status = '';
    public $catatan = '';

    protected $queryString = [
        'status_filter' => ['except' => ''],
        'user_filter' => ['except' => ''],
        'mulai' => ['except' => ''],
        'akhir' => ['except' => ''],
    ];

    public function mount()
    {
        date_default_timezone_set('Asia/Jakarta');
        if (!$this->mulai) {
            $this->mulai = date('Y-m-d', strtotime('-7 days'));
        }
        if (!$this->akhir) {
            $this->akhir = date('Y-m-d');
        }
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function updatingUserFilter()
    {
        $this->resetPage();
    }

    public function updatingMulai()
    {
        $this->resetPage();
    }

    public function updatingAkhir()
    {
        $this->resetPage();
    }

    public function resetFilters()
    {
        $this->status_filter = '';
        $this->user_filter = '';
        $this->mulai = date('Y-m-d', strtotime('-7 days'));
        $this->akhir = date('Y-m-d');
        $this->resetPage();
    }

    public function openApprovalModal($leaveId)
    {
        $this->selectedLeave = Cuti::with('User')->find($leaveId);
        $this->approval_status = '';
        $this->catatan = '';
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->selectedLeave = null;
        $this->approval_status = '';
        $this->catatan = '';
    }

    public function processApproval()
    {
        $this->validate([
            'approval_status' => 'required|in:Diterima,Ditolak',
            'catatan' => 'nullable|string|max:500',
        ]);

        if ($this->selectedLeave) {
            $this->selectedLeave->update([
                'status_cuti' => $this->approval_status,
                'catatan' => $this->catatan,
            ]);

            // Update mapping shift if approved
            if ($this->approval_status === 'Diterima') {
                $this->updateMappingShift($this->selectedLeave);
            }

            // Send notification (simplified version)
            $this->sendNotification($this->selectedLeave);

            session()->flash('success', 'Pengajuan berhasil ' . strtolower($this->approval_status));
            $this->closeModal();
        }
    }

    private function updateMappingShift($cuti)
    {
        $mapping_shift = MappingShift::where('tanggal', $cuti->tanggal)
            ->where('user_id', $cuti->user_id)
            ->first();

        if ($mapping_shift) {
            $mapping_shift->update([
                'status_absen' => $cuti->nama_cuti,
            ]);
        }
    }

    private function sendNotification($cuti)
    {
        // Simplified notification - in real implementation, you'd use proper notification system
        // This is just a placeholder for the notification logic
    }

    public function deleteLeave($id)
    {
        $leave = Cuti::find($id);
        if ($leave) {
            if ($leave->foto_cuti) {
                Storage::delete($leave->foto_cuti);
            }
            $leave->delete();
            session()->flash('success', 'Data berhasil dihapus');
        }
    }

    public function render()
    {
        // Get users for filter
        $users = User::select('id', 'name')->where('is_admin', 'user')->orderBy('name')->get();

        // Build query
        $query = Cuti::with(['User.Jabatan', 'User.Lokasi']);

        // Apply filters
        if ($this->status_filter) {
            $query->where('status_cuti', $this->status_filter);
        }

        if ($this->user_filter) {
            $query->where('user_id', $this->user_filter);
        }

        if ($this->mulai) {
            $query->where('tanggal', '>=', $this->mulai);
        }

        if ($this->akhir) {
            $query->where('tanggal', '<=', $this->akhir);
        }

        $data_cuti = $query->orderBy('created_at', 'desc')->paginate($this->perPage);

        return view('livewire.leave.leave-management', [
            'data_cuti' => $data_cuti,
            'users' => $users,
        ]);
    }
}
